import { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../lib/supabase';

export interface DashboardData {
  totalShares: number;
  totalInvested: number;
  currentValue: number;
  unrealizedGains: number;
  unrealizedGainsPercent: number;
  averageCostPerShare: number;
  projectedAnnualDividend: number;
  usdtCommissions?: {
    available: number;
    pending: number;
    total: number;
  };
  shareCommissions?: {
    totalShares: number;
    pendingShares: number;
  };
  currentSharePrice: number;
  notifications?: {
    unread: number;
    total: number;
  };
  kycStatus?: string;
  telegramConnected?: boolean;
}

export interface UseDashboardDataReturn {
  dashboardData: DashboardData;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useDashboardData = (userId: number): UseDashboardDataReturn => {
  const [dashboardData, setDashboardData] = useState<DashboardData>({
    totalShares: 0,
    totalInvested: 0,
    currentValue: 0,
    unrealizedGains: 0,
    unrealizedGainsPercent: 0,
    averageCostPerShare: 0,
    projectedAnnualDividend: 0,
    currentSharePrice: 5.00,
    usdtCommissions: {
      available: 0,
      pending: 0,
      total: 0
    },
    shareCommissions: {
      totalShares: 0,
      pendingShares: 0
    },
    notifications: {
      unread: 0,
      total: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadDashboardData = async () => {
    if (!userId) return;

    try {
      setLoading(true);
      setError(null);

      const serviceClient = getServiceRoleClient();

      // Load share purchases
      const { data: sharePurchases, error: sharesError } = await serviceClient
        .from('aureus_share_purchases')
        .select(`
          shares_purchased,
          total_amount,
          price_per_share,
          status
        `)
        .eq('user_id', userId)
        .eq('status', 'active');

      if (sharesError) {
        console.error('Error loading share purchases:', sharesError);
        throw new Error('Failed to load share purchases');
      }

      // Load commission balances
      const { data: commissionBalance, error: commissionError } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (commissionError && commissionError.code !== 'PGRST116') {
        console.error('Error loading commission balance:', commissionError);
      }

      // Load current phase for share price
      const { data: currentPhase, error: phaseError } = await serviceClient
        .from('investment_phases')
        .select('*')
        .eq('is_active', true)
        .single();

      if (phaseError) {
        console.error('Error loading current phase:', phaseError);
      }

      // Load notifications count
      const { count: unreadNotifications } = await serviceClient
        .from('user_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false);

      const { count: totalNotifications } = await serviceClient
        .from('user_notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId);

      // Load KYC status
      const { data: kycData, error: kycError } = await serviceClient
        .from('kyc_information')
        .select('verification_status')
        .eq('user_id', userId)
        .single();

      // Calculate portfolio metrics
      const purchasedShares = sharePurchases?.reduce((sum, purchase) => sum + purchase.shares_purchased, 0) || 0;
      const commissionShares = commissionBalance?.share_balance || 0;
      const totalShares = purchasedShares + commissionShares; // Include both purchased and commission shares
      const totalInvested = sharePurchases?.reduce((sum, purchase) => sum + purchase.total_amount, 0) || 0;
      const currentSharePrice = currentPhase?.price_per_share || 5.00;
      const currentValue = totalShares * currentSharePrice;
      const unrealizedGains = currentValue - totalInvested;
      const unrealizedGainsPercent = totalInvested > 0 ? (unrealizedGains / totalInvested) * 100 : 0;
      const averageCostPerShare = totalShares > 0 ? totalInvested / totalShares : 0;

      // Simple dividend projection (this would be more complex in reality)
      const projectedAnnualDividend = totalShares * 0.5; // Placeholder calculation

      setDashboardData({
        totalShares,
        totalInvested,
        currentValue,
        unrealizedGains,
        unrealizedGainsPercent,
        averageCostPerShare,
        projectedAnnualDividend,
        currentSharePrice,
        usdtCommissions: {
          available: commissionBalance?.usdt_balance || 0,
          pending: commissionBalance?.escrowed_amount || 0,
          total: commissionBalance?.total_earned_usdt || 0
        },
        shareCommissions: {
          totalShares: commissionBalance?.share_balance || 0,
          pendingShares: 0 // Would need additional logic for pending shares
        },
        notifications: {
          unread: unreadNotifications || 0,
          total: totalNotifications || 0
        },
        kycStatus: kycData?.verification_status || 'not_started',
        telegramConnected: false // Would need to check telegram connection
      });

    } catch (err: any) {
      console.error('Dashboard data loading error:', err);
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, [userId]);

  return {
    dashboardData,
    loading,
    error,
    refetch: loadDashboardData
  };
};
