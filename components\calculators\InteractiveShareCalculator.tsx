import React, { useState, useEffect } from 'react';

interface InteractiveShareCalculatorProps {
  className?: string;
}

export const InteractiveShareCalculator: React.FC<InteractiveShareCalculatorProps> = ({ className = '' }) => {
  const [shareAmount, setShareAmount] = useState<number>(1000);
  const [selectedPhase, setSelectedPhase] = useState<number>(1);
  const [goldPrice] = useState<number>(109026); // Current gold price per kg
  
  // Phase data with share prices
  const phases = [
    { phase: 1, sharePrice: 1.00, description: "Foundation Phase" },
    { phase: 2, sharePrice: 1.25, description: "Early Growth" },
    { phase: 3, sharePrice: 1.50, description: "Expansion Phase" },
    { phase: 4, sharePrice: 1.75, description: "Scale Phase" },
    { phase: 5, sharePrice: 2.00, description: "Maturity Phase" }
  ];

  const currentPhase = phases.find(p => p.phase === selectedPhase) || phases[0];
  
  // Calculations
  const totalCost = shareAmount * currentPhase.sharePrice;
  const annualDividendRate = 0.12; // 12% annual dividend
  const monthlyDividendRate = annualDividendRate / 12;
  const monthlyDividend = totalCost * monthlyDividendRate;
  const annualDividend = totalCost * annualDividendRate;
  
  // Gold backing calculation (assuming 1g gold per share at maturity)
  const goldBacking = shareAmount * (goldPrice / 1000); // Convert to grams
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className={`interactive-calculator ${className}`}>
      <div className="calculator-header">
        <h3>Calculate Your Potential Returns</h3>
        <p>Discover how many shares you want to purchase and see your potential dividends</p>
      </div>

      <div className="calculator-controls">
        <div className="input-group">
          <label htmlFor="shareAmount">Number of Shares</label>
          <div className="input-wrapper">
            <input
              type="number"
              id="shareAmount"
              value={shareAmount}
              onChange={(e) => setShareAmount(Math.max(1, parseInt(e.target.value) || 1))}
              min="1"
              step="100"
            />
            <div className="input-controls">
              <button 
                type="button" 
                onClick={() => setShareAmount(prev => Math.max(1, prev - 100))}
                className="control-btn"
              >
                -
              </button>
              <button 
                type="button" 
                onClick={() => setShareAmount(prev => prev + 100)}
                className="control-btn"
              >
                +
              </button>
            </div>
          </div>
        </div>

        <div className="input-group">
          <label htmlFor="phaseSelect">Investment Phase</label>
          <select
            id="phaseSelect"
            value={selectedPhase}
            onChange={(e) => setSelectedPhase(parseInt(e.target.value))}
          >
            {phases.map(phase => (
              <option key={phase.phase} value={phase.phase}>
                Phase {phase.phase} - ${phase.sharePrice}/share ({phase.description})
              </option>
            ))}
          </select>
        </div>

        <div className="quick-amounts">
          <span>Quick Select:</span>
          {[500, 1000, 2500, 5000, 10000].map(amount => (
            <button
              key={amount}
              onClick={() => setShareAmount(amount)}
              className={`quick-btn ${shareAmount === amount ? 'active' : ''}`}
            >
              {amount.toLocaleString()}
            </button>
          ))}
        </div>
      </div>

      <div className="calculation-results">
        <div className="result-grid">
          <div className="result-card primary">
            <div className="result-label">Total Investment</div>
            <div className="result-value">{formatCurrency(totalCost)}</div>
            <div className="result-detail">{shareAmount.toLocaleString()} shares × ${currentPhase.sharePrice}</div>
          </div>

          <div className="result-card">
            <div className="result-label">Monthly Dividends</div>
            <div className="result-value">{formatCurrency(monthlyDividend)}</div>
            <div className="result-detail">1% monthly return</div>
          </div>

          <div className="result-card">
            <div className="result-label">Annual Dividends</div>
            <div className="result-value">{formatCurrency(annualDividend)}</div>
            <div className="result-detail">12% annual return</div>
          </div>

          <div className="result-card">
            <div className="result-label">Gold Backing Value</div>
            <div className="result-value">{formatCurrency(goldBacking)}</div>
            <div className="result-detail">Based on current gold price</div>
          </div>
        </div>

        <div className="projection-timeline">
          <h4>5-Year Projection</h4>
          <div className="timeline-grid">
            {[1, 2, 3, 4, 5].map(year => (
              <div key={year} className="timeline-item">
                <div className="year">Year {year}</div>
                <div className="dividend">{formatCurrency(annualDividend * year)}</div>
                <div className="total">Total Received</div>
              </div>
            ))}
          </div>
        </div>

        <div className="action-section">
          <button className="purchase-btn">
            Purchase {shareAmount.toLocaleString()} Shares
          </button>
          <p className="disclaimer">
            * Dividend projections are estimates based on current performance. 
            Actual returns may vary based on mining operations and market conditions.
          </p>
        </div>
      </div>

      <style jsx>{`
        .interactive-calculator {
          background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
          border-radius: 20px;
          padding: 40px;
          margin: 40px 0;
          border: 1px solid rgba(212, 175, 55, 0.2);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .calculator-header {
          text-align: center;
          margin-bottom: 40px;
        }

        .calculator-header h3 {
          color: #d4af37;
          font-size: 2.2rem;
          margin-bottom: 10px;
          font-weight: 700;
        }

        .calculator-header p {
          color: #b0b0b0;
          font-size: 1.1rem;
        }

        .calculator-controls {
          margin-bottom: 40px;
        }

        .input-group {
          margin-bottom: 25px;
        }

        .input-group label {
          display: block;
          color: #d4af37;
          font-weight: 600;
          margin-bottom: 8px;
          font-size: 1.1rem;
        }

        .input-wrapper {
          position: relative;
          display: flex;
          align-items: center;
        }

        .input-wrapper input,
        .input-group select {
          background: rgba(255, 255, 255, 0.1);
          border: 2px solid rgba(212, 175, 55, 0.3);
          border-radius: 12px;
          padding: 15px 20px;
          color: white;
          font-size: 1.1rem;
          width: 100%;
          transition: all 0.3s ease;
        }

        .input-wrapper input:focus,
        .input-group select:focus {
          outline: none;
          border-color: #d4af37;
          box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
        }

        .input-controls {
          display: flex;
          margin-left: 10px;
        }

        .control-btn {
          background: #d4af37;
          border: none;
          border-radius: 8px;
          color: #1a1a2e;
          width: 40px;
          height: 40px;
          font-size: 1.2rem;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
          margin: 0 2px;
        }

        .control-btn:hover {
          background: #f4d03f;
          transform: translateY(-2px);
        }

        .quick-amounts {
          display: flex;
          align-items: center;
          gap: 10px;
          flex-wrap: wrap;
          margin-top: 20px;
        }

        .quick-amounts span {
          color: #b0b0b0;
          font-weight: 600;
        }

        .quick-btn {
          background: rgba(212, 175, 55, 0.1);
          border: 1px solid rgba(212, 175, 55, 0.3);
          border-radius: 8px;
          color: #d4af37;
          padding: 8px 16px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-weight: 600;
        }

        .quick-btn:hover,
        .quick-btn.active {
          background: #d4af37;
          color: #1a1a2e;
          transform: translateY(-2px);
        }

        .calculation-results {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 16px;
          padding: 30px;
          border: 1px solid rgba(212, 175, 55, 0.1);
        }

        .result-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }

        .result-card {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 20px;
          text-align: center;
          border: 1px solid rgba(212, 175, 55, 0.2);
          transition: all 0.3s ease;
        }

        .result-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px rgba(212, 175, 55, 0.1);
        }

        .result-card.primary {
          border-color: #d4af37;
          background: rgba(212, 175, 55, 0.1);
        }

        .result-label {
          color: #b0b0b0;
          font-size: 0.9rem;
          margin-bottom: 8px;
          text-transform: uppercase;
          letter-spacing: 1px;
        }

        .result-value {
          color: #d4af37;
          font-size: 1.8rem;
          font-weight: 700;
          margin-bottom: 5px;
        }

        .result-detail {
          color: #888;
          font-size: 0.85rem;
        }

        .projection-timeline {
          margin: 30px 0;
        }

        .projection-timeline h4 {
          color: #d4af37;
          text-align: center;
          margin-bottom: 20px;
          font-size: 1.3rem;
        }

        .timeline-grid {
          display: grid;
          grid-template-columns: repeat(5, 1fr);
          gap: 15px;
        }

        .timeline-item {
          background: rgba(212, 175, 55, 0.1);
          border-radius: 10px;
          padding: 15px;
          text-align: center;
          border: 1px solid rgba(212, 175, 55, 0.2);
        }

        .timeline-item .year {
          color: #d4af37;
          font-weight: 600;
          margin-bottom: 5px;
        }

        .timeline-item .dividend {
          color: white;
          font-size: 1.1rem;
          font-weight: 700;
          margin-bottom: 3px;
        }

        .timeline-item .total {
          color: #888;
          font-size: 0.8rem;
        }

        .action-section {
          text-align: center;
          margin-top: 30px;
        }

        .purchase-btn {
          background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
          border: none;
          border-radius: 12px;
          color: #1a1a2e;
          padding: 18px 40px;
          font-size: 1.2rem;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-bottom: 15px;
        }

        .purchase-btn:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
        }

        .disclaimer {
          color: #888;
          font-size: 0.85rem;
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.4;
        }

        @media (max-width: 768px) {
          .interactive-calculator {
            padding: 25px;
            margin: 20px 0;
          }

          .calculator-header h3 {
            font-size: 1.8rem;
          }

          .timeline-grid {
            grid-template-columns: repeat(2, 1fr);
          }

          .result-grid {
            grid-template-columns: 1fr;
          }

          .quick-amounts {
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default InteractiveShareCalculator;
