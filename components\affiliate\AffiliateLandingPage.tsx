import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import InteractiveShareCalculator from '../calculators/InteractiveShareCalculator';

interface AffiliateLandingPageProps {
  username: string;
}

interface AffiliateProfile {
  id: number;
  username: string;
  first_name?: string;
  last_name?: string;
  email: string;
  phone?: string;
  profile_description?: string;
  profile_image_url?: string;
  total_referrals: number;
  total_earnings: number;
  created_at: string;
}

interface RegistrationData {
  fullName: string;
  email: string;
  phone: string;
  countryOfResidence: string;
  password: string;
  confirmPassword: string;
}

export const AffiliateLandingPage: React.FC<AffiliateLandingPageProps> = ({ username }) => {
  const [affiliate, setAffiliate] = useState<AffiliateProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [showRegistration, setShowRegistration] = useState(false);
  const [registrationData, setRegistrationData] = useState<RegistrationData>({
    fullName: '',
    email: '',
    phone: '',
    countryOfResidence: '',
    password: '',
    confirmPassword: ''
  });
  const [registering, setRegistering] = useState(false);

  useEffect(() => {
    loadAffiliateProfile();
  }, [username]);

  // Refresh affiliate data when page becomes visible (to catch profile picture updates)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && affiliate) {
        loadAffiliateProfile();
      }
    };

    const handleFocus = () => {
      if (affiliate) {
        loadAffiliateProfile();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [affiliate]);

  const loadAffiliateProfile = async () => {
    try {
      const { data: affiliateData, error } = await supabase
        .from('users')
        .select(`
          id, username, first_name, last_name, email, phone,
          profile_description, profile_image_url, total_referrals,
          total_earnings, created_at, updated_at
        `)
        .eq('username', username)
        .single();

      if (error) throw error;
      setAffiliate(affiliateData);
    } catch (error) {
      console.error('Error loading affiliate profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRegistration = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (registrationData.password !== registrationData.confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    if (!affiliate) {
      alert('Affiliate information not found');
      return;
    }

    setRegistering(true);
    try {
      // Register user with automatic sponsor assignment
      const response = await fetch('/api/register-with-sponsor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...registrationData,
          sponsorId: affiliate.id,
          sponsorUsername: affiliate.username
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        alert('Registration successful! You have been automatically connected to your sponsor.');
        // Redirect to login or dashboard
        window.location.href = '/login';
      } else {
        alert(result.message || 'Registration failed. Please try again.');
      }
    } catch (error) {
      console.error('Registration error:', error);
      alert('Registration failed. Please try again.');
    } finally {
      setRegistering(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">⏳</div>
          <div className="text-white text-xl">Loading affiliate profile...</div>
        </div>
      </div>
    );
  }

  if (!affiliate) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="text-4xl mb-4">❌</div>
          <div className="text-white text-xl mb-4">Affiliate Not Found</div>
          <div className="text-gray-400">The affiliate profile "{username}" could not be found.</div>
        </div>
      </div>
    );
  }

  return (
    <div className="professional-landing-page">
      {/* Navigation Header */}
      <header className="main-header">
        <div className="header-container">
          <div className="logo-section">
            <img
              src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-s.png"
              alt="Aureus Alliance Holdings"
              className="header-logo"
            />
          </div>
          <nav className="main-nav">
            <div className="nav-links">
              <a href="#about" className="nav-link">About</a>
              <a href="#operations" className="nav-link">Operations</a>
              <a href="#calculator" className="nav-link">Calculator</a>
              <a href="#phases" className="nav-link">Phases</a>
            </div>
            <button
              onClick={() => setShowRegistration(true)}
              className="header-cta-btn"
            >
              Become a Shareholder
            </button>
          </nav>
        </div>
      </header>

      {/* Professional Hero Section - Meet Your Personal Advisor */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="content">
              <div className="badge">
                <span className="badge-dot"></span>
                Your Personal Advisor
              </div>
              <h1 className="hero-title">
                Meet {affiliate.first_name && affiliate.last_name
                  ? `${affiliate.first_name} ${affiliate.last_name}`
                  : affiliate.username
                }
              </h1>
              <p className="hero-subtitle">
                {affiliate.profile_description ||
                 "Your dedicated guide to gold mining share ownership with Aureus Alliance Holdings. Professional expertise in helping investors secure their financial future through real gold-backed shares."
                }
              </p>

              {/* Professional Metrics */}
              <div className="metrics-grid">
                <div className="metric-card">
                  <div className="metric-value">{affiliate.total_referrals}</div>
                  <div className="metric-label">Clients Helped</div>
                </div>
                <div className="metric-card">
                  <div className="metric-value">${affiliate.total_earnings.toLocaleString()}</div>
                  <div className="metric-label">Total Earnings</div>
                </div>
                <div className="metric-card">
                  <div className="metric-value">@{affiliate.username}</div>
                  <div className="metric-label">Advisor Code</div>
                </div>
                {affiliate.phone && (
                  <div className="metric-card">
                    <div className="metric-value">📞</div>
                    <div className="metric-label">{affiliate.phone}</div>
                  </div>
                )}
              </div>

              {/* CTA Buttons */}
              <div className="cta-buttons">
                <button
                  onClick={() => setShowRegistration(true)}
                  className="btn btn-primary"
                >
                  Start Your Journey
                </button>
                <button
                  onClick={() => {
                    const calculatorSection = document.getElementById('calculator-section');
                    calculatorSection?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  className="btn btn-secondary"
                >
                  Calculate Earnings
                </button>
              </div>
            </div>

            <div className="logo">
              <div className="advisor-profile-image">
                {affiliate.profile_image_url ? (
                  <img
                    src={`${affiliate.profile_image_url}?t=${new Date(affiliate.updated_at || Date.now()).getTime()}`}
                    alt={`${affiliate.first_name} ${affiliate.last_name}`}
                    className="profile-hero-image"
                  />
                ) : (
                  <div className="profile-hero-placeholder">
                    {(affiliate.first_name?.[0] || affiliate.username[0]).toUpperCase()}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Company Overview Section */}
      <section className="overview">
        <div className="container">
          <div className="section-header">
            <h2>About Aureus Alliance Holdings</h2>
            <p>CIPC-registered gold mining company building wealth through real operations</p>
          </div>

          <div className="overview-grid">
            <div className="overview-card">
              <div className="card-icon">🏗️</div>
              <h3>Operations Timeline</h3>
              <ul>
                <li><strong>January 2026:</strong> Operations begin with 2 wash plants</li>
                <li><strong>April 2026:</strong> First dividend payout ($15-$50 per share)</li>
                <li><strong>June 2026:</strong> Operations scale to 10 wash plants</li>
                <li><strong>2030:</strong> 200+ plants across 5,000 hectares</li>
                <li><strong>Multi-Country:</strong> Zimbabwe, Zambia, Ghana, Tanzania, South Africa</li>
              </ul>
            </div>

            <div className="overview-card">
              <div className="card-icon">💎</div>
              <h3>Share Structure</h3>
              <ul>
                <li><strong>Total Shares:</strong> 1.4 million available</li>
                <li><strong>Presale Price:</strong> $5.00 per share</li>
                <li><strong>Presale Allocation:</strong> 200,000 shares</li>
                <li><strong>Legal Structure:</strong> CIPC-registered company</li>
                <li><strong>Ownership:</strong> Real shares, real ownership</li>
              </ul>
            </div>

            <div className="overview-card">
              <div className="card-icon">📈</div>
              <h3>Growth Projections</h3>
              <ul>
                <li><strong>2026:</strong> 10 plants (250 hectares)</li>
                <li><strong>2027:</strong> 25 plants (625 hectares)</li>
                <li><strong>2028:</strong> 50 plants (1,250 hectares)</li>
                <li><strong>2029:</strong> 100 plants (2,500 hectares)</li>
                <li><strong>2030:</strong> 200 plants (5,000 hectares)</li>
              </ul>
            </div>

            <div className="overview-card">
              <div className="card-icon">🎥</div>
              <h3>See Our Operations</h3>
              <div className="video-container">
                <video
                  controls
                  className="operations-video"
                  poster="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/washplant-thumbnail.jpg"
                >
                  <source src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/washplant-operations.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
              <p className="video-description">
                Watch our wash plant operations and see the future of gold mining
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Calculator Section */}
      <section id="calculator-section" className="content-section">
        <div className="container">
          <div className="section-header">
            <h2>Calculate Your Potential Earnings</h2>
            <p>See projected dividends and returns based on your share purchase</p>
          </div>

          <div className="calculator-container">
            <ComprehensiveDividendsCalculator userShares={1000} />
          </div>
        </div>
      </section>

      {/* Professional Registration Modal */}
      {showRegistration && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Join Aureus Alliance Holdings</h3>
              <button
                onClick={() => setShowRegistration(false)}
                className="modal-close"
              >
                ×
              </button>
            </div>

            <div className="advisor-connection-notice">
              <div className="notice-icon">🤝</div>
              <div className="notice-content">
                <h4>Your Personal Advisor</h4>
                <p>
                  You will be automatically connected to <strong>{affiliate.first_name} {affiliate.last_name}</strong>
                  as your dedicated advisor throughout your journey with Aureus Alliance Holdings.
                </p>
              </div>
            </div>

            <form onSubmit={handleRegistration} className="space-y-4">
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">Full Name</label>
                <input
                  type="text"
                  required
                  value={registrationData.fullName}
                  onChange={(e) => setRegistrationData({...registrationData, fullName: e.target.value})}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                />
              </div>

              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">Email</label>
                <input
                  type="email"
                  required
                  value={registrationData.email}
                  onChange={(e) => setRegistrationData({...registrationData, email: e.target.value})}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                />
              </div>

              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">Phone</label>
                <input
                  type="tel"
                  required
                  value={registrationData.phone}
                  onChange={(e) => setRegistrationData({...registrationData, phone: e.target.value})}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                />
              </div>

              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">Country</label>
                <select
                  required
                  value={registrationData.countryOfResidence}
                  onChange={(e) => setRegistrationData({...registrationData, countryOfResidence: e.target.value})}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                >
                  <option value="">Select Country</option>
                  <option value="South Africa">South Africa</option>
                  <option value="United States">United States</option>
                  <option value="United Kingdom">United Kingdom</option>
                  <option value="Canada">Canada</option>
                  <option value="Australia">Australia</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">Password</label>
                <input
                  type="password"
                  required
                  value={registrationData.password}
                  onChange={(e) => setRegistrationData({...registrationData, password: e.target.value})}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                />
              </div>

              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">Confirm Password</label>
                <input
                  type="password"
                  required
                  value={registrationData.confirmPassword}
                  onChange={(e) => setRegistrationData({...registrationData, confirmPassword: e.target.value})}
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-yellow-500"
                />
              </div>

              <button
                type="submit"
                disabled={registering}
                className="w-full bg-yellow-500 hover:bg-yellow-600 text-black py-3 rounded-lg font-semibold transition-colors disabled:opacity-50"
              >
                {registering ? 'Creating Account...' : 'Create Account & Connect'}
              </button>
            </form>
          </div>
        </div>
      )}

      {/* Professional Call to Action */}
      <section className="cta-section">
        <div className="container">
          <div className="cta-content">
            <h2>Ready to Own Real Gold Mining Shares?</h2>
            <p>
              Join thousands of shareholders who are building wealth through real gold mining operations.
              Your personal advisor {affiliate.first_name} {affiliate.last_name} is ready to guide you.
            </p>
            <div className="cta-buttons">
              <button
                onClick={() => setShowRegistration(true)}
                className="btn btn-primary btn-large"
              >
                Start Your Journey Today
              </button>
              <button
                onClick={() => {
                  const calculatorSection = document.getElementById('calculator-section');
                  calculatorSection?.scrollIntoView({ behavior: 'smooth' });
                }}
                className="btn btn-secondary btn-large"
              >
                Calculate Returns First
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Footer */}
      <footer className="clean-footer">
        <div className="container">
          <div className="footer-content">
            <div className="footer-info">
              <img
                src="https://fgubaqoftdeefcakejwu.supabase.co/storage/v1/object/public/assets/logo-m.png"
                alt="Aureus Alliance Holdings"
                className="footer-logo"
              />
              <h3>Aureus Alliance Holdings (Pty) Ltd</h3>
              <p>CIPC-registered gold mining company • Real shares backed by real gold production</p>
            </div>
            <div className="footer-links">
              <div className="footer-section">
                <h4>Your Advisor</h4>
                <p>{affiliate.first_name} {affiliate.last_name}</p>
                <p>@{affiliate.username}</p>
                {affiliate.phone && <p>📞 {affiliate.phone}</p>}
              </div>
              <div className="footer-section">
                <h4>Company</h4>
                <p>Real Gold Mining</p>
                <p>Transparent Operations</p>
                <p>Professional Management</p>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};
