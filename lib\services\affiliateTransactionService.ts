/**
 * AFFILIATE TRANSACTION SERVICE
 * 
 * Comprehensive transaction management for affiliate operations:
 * - Commission to shares conversion with atomic operations
 * - Share transfers between users with validation
 * - Commission withdrawal processing
 * - Audit logging for all financial transactions
 * - Rollback mechanisms for failed operations
 */

import { supabase, getServiceRoleClient } from '../supabase';
import { affiliateNotificationService } from './affiliateNotificationService';

// 🔒 ESCROW SECURITY FUNCTIONS - Prevent Double-Spending Vulnerabilities
// These functions implement atomic escrow operations to prevent race conditions
// between concurrent commission withdrawal and conversion requests

/**
 * Atomically check available balance and create escrow for commission request
 * @param {number} userId - User ID
 * @param {number} requestAmount - Amount to escrow
 * @param {string} requestType - 'withdrawal' or 'conversion'
 * @returns {Promise<{success: boolean, availableBalance?: number, error?: string}>}
 */
async function createCommissionEscrow(userId: number, requestAmount: number, requestType: string): Promise<{success: boolean, availableBalance?: number, error?: string}> {
  try {
    const serviceClient = getServiceRoleClient();

    // Use a database transaction to atomically check and update escrow
    const { data, error } = await serviceClient.rpc('create_commission_escrow', {
      p_user_id: userId,
      p_request_amount: requestAmount,
      p_request_type: requestType
    });

    if (error) {
      console.error(`❌ [ESCROW] Failed to create escrow for ${requestType}:`, error);
      return { success: false, error: error.message };
    }

    console.log(`✅ [ESCROW] Created ${requestType} escrow: $${requestAmount} for user ${userId}`);
    return { success: true, availableBalance: data };
  } catch (error) {
    console.error(`❌ [ESCROW] Exception creating escrow:`, error);
    return { success: false, error: 'Internal escrow error' };
  }
}

/**
 * Release escrow when request is rejected (only deduct from escrowed_amount)
 * @param {number} userId - User ID
 * @param {number} escrowAmount - Amount to release from escrow
 * @returns {Promise<{success: boolean, error?: string}>}
 */
async function releaseCommissionEscrow(userId: number, escrowAmount: number): Promise<{success: boolean, error?: string}> {
  try {
    const serviceClient = getServiceRoleClient();

    const { error } = await serviceClient.rpc('release_commission_escrow', {
      p_user_id: userId,
      p_escrow_amount: escrowAmount
    });

    if (error) {
      console.error(`❌ [ESCROW] Failed to release escrow:`, error);
      return { success: false, error: error.message };
    }

    console.log(`✅ [ESCROW] Released escrow: $${escrowAmount} for user ${userId}`);
    return { success: true };
  } catch (error) {
    console.error(`❌ [ESCROW] Exception releasing escrow:`, error);
    return { success: false, error: 'Internal escrow error' };
  }
}

/**
 * Get available commission balance (usdt_balance - escrowed_amount)
 * @param {number} userId - User ID
 * @returns {Promise<{availableBalance: number, totalBalance: number, escrowedAmount: number}>}
 */
async function getAvailableCommissionBalance(userId: number): Promise<{availableBalance: number, totalBalance: number, escrowedAmount: number}> {
  try {
    const serviceClient = getServiceRoleClient();

    const { data: balance, error } = await serviceClient
      .from('commission_balances')
      .select('usdt_balance, escrowed_amount')
      .eq('user_id', userId)
      .single();

    if (error || !balance) {
      console.log(`💰 [ESCROW] No balance found for User ${userId}`);
      return { availableBalance: 0, totalBalance: 0, escrowedAmount: 0 };
    }

    const totalBalance = parseFloat(balance.usdt_balance || 0);
    const escrowedAmount = parseFloat(balance.escrowed_amount || 0);
    const availableBalance = totalBalance - escrowedAmount;

    console.log(`💰 [ESCROW] Balance check for user ${userId}: Total=$${totalBalance}, Escrowed=$${escrowedAmount}, Available=$${availableBalance}`);

    return {
      availableBalance: Math.max(0, availableBalance),
      totalBalance,
      escrowedAmount
    };
  } catch (error) {
    console.error(`❌ [ESCROW] Exception getting balance:`, error);
    return { availableBalance: 0, totalBalance: 0, escrowedAmount: 0 };
  }
}

/**
 * Validate that user is not trying to be their own sponsor (except userID 4)
 * @param {number} userId - User ID
 * @param {string} sponsorUsername - Sponsor username to validate
 * @returns {Promise<{isValid: boolean, error?: string}>}
 */
async function validateSponsorAssignment(userId: number, sponsorUsername: string): Promise<{isValid: boolean, error?: string}> {
  try {
    const serviceClient = getServiceRoleClient();

    // Get the sponsor user
    const { data: sponsorUser, error: sponsorError } = await serviceClient
      .from('users')
      .select('id, username')
      .eq('username', sponsorUsername.toLowerCase().trim())
      .single();

    if (sponsorError || !sponsorUser) {
      return { isValid: false, error: 'Sponsor username not found' };
    }

    // Check if user is trying to be their own sponsor (except userID 4)
    if (sponsorUser.id === userId && userId !== 4) {
      return { isValid: false, error: 'You cannot be your own sponsor' };
    }

    return { isValid: true };
  } catch (error) {
    console.error(`❌ [SPONSOR_VALIDATION] Exception validating sponsor:`, error);
    return { isValid: false, error: 'Error validating sponsor' };
  }
}

export interface ConversionTransaction {
  userId: number;
  usdtAmount: number;
  sharesReceived: number;
  sharePrice: number;
  phaseId: number;
  transactionId?: string;
}

export interface ShareTransferTransaction {
  senderId: number;
  recipientId: number;
  sharesTransferred: number;
  transferFee: number;
  totalDeducted: number;
  transactionId?: string;
}

export interface WithdrawalTransaction {
  userId: number;
  amount: number;
  currency: 'USDT' | 'SHARES';
  walletAddress?: string;
  network?: string;
  transactionId?: string;
}

export interface TransactionResult {
  success: boolean;
  transactionId?: string;
  error?: string;
  data?: any;
}

class AffiliateTransactionService {
  /**
   * Convert USDT commission to shares with atomic operations
   */
  async processCommissionToSharesConversion(transaction: ConversionTransaction): Promise<TransactionResult> {
    const serviceClient = getServiceRoleClient();

    try {
      console.log('🔄 Processing USDT to Shares conversion:', transaction);

      // 🔒 SECURE ESCROW: Check available balance and create escrow atomically
      console.log(`🔒 [ESCROW] Creating commission conversion escrow for user ${transaction.userId}, amount: $${transaction.usdtAmount}`);

      const escrowResult = await createCommissionEscrow(transaction.userId, transaction.usdtAmount, 'conversion');

      if (!escrowResult.success) {
        console.error(`❌ [ESCROW] Failed to create conversion escrow:`, escrowResult.error);
        throw new Error(escrowResult.error || 'Failed to secure funds for conversion');
      }

      // Get current balance for processing
      const { data: currentBalance, error: balanceError } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', transaction.userId)
        .single();

      if (balanceError || !currentBalance) {
        // 🔒 ROLLBACK: Release escrow if balance check failed
        await releaseCommissionEscrow(transaction.userId, transaction.usdtAmount);
        throw new Error('Failed to get current balance');
      }

      // Calculate new balances
      const newUsdtBalance = currentBalance.usdt_balance - transaction.usdtAmount;
      const newShareBalance = currentBalance.share_balance + transaction.sharesReceived;
      const newTotalEarnedShares = (currentBalance.total_earned_shares || 0) + transaction.sharesReceived;

      // Get phase number for the conversion
      const { data: phaseData, error: phaseError } = await serviceClient
        .from('investment_phases')
        .select('phase_number')
        .eq('id', transaction.phaseId)
        .single();

      if (phaseError || !phaseData) {
        throw new Error('Failed to get phase information for conversion');
      }

      // Create conversion record in commission_conversions table as PENDING for admin approval
      const { data: conversionRecord, error: conversionError } = await serviceClient
        .from('commission_conversions')
        .insert({
          user_id: transaction.userId,
          shares_requested: Math.floor(transaction.sharesReceived), // Convert to integer
          usdt_amount: transaction.usdtAmount,
          share_price: transaction.sharePrice,
          phase_id: transaction.phaseId,
          phase_number: phaseData.phase_number,
          status: 'pending', // Changed to pending for admin approval
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (conversionError) {
        throw conversionError;
      }

      // Update commission balance - only deduct USDT, shares will be added upon admin approval
      const { error: updateError } = await serviceClient
        .from('commission_balances')
        .update({
          usdt_balance: newUsdtBalance, // Deduct USDT immediately
          // share_balance and total_earned_shares will be updated upon admin approval
          last_updated: new Date().toISOString()
        })
        .eq('user_id', transaction.userId);

      if (updateError) {
        throw updateError;
      }

      // Log audit trail for pending conversion
      await this.logAuditTrail({
        user_id: transaction.userId,
        action: 'commission_conversion_pending',
        details: {
          usdt_converted: transaction.usdtAmount,
          shares_requested: transaction.sharesReceived,
          share_price: transaction.sharePrice,
          transaction_id: conversionRecord.id,
          status: 'pending_admin_approval'
        },
        amount: transaction.usdtAmount,
        currency: 'USDT'
      });

      console.log('✅ USDT to Shares conversion request submitted for admin approval');

      return {
        success: true,
        transactionId: conversionRecord.id,
        data: {
          newUsdtBalance,
          newShareBalance,
          sharesReceived: transaction.sharesReceived
        }
      };

    } catch (error) {
      console.error('❌ Error processing USDT to Shares conversion:', error);
      
      // Log failed transaction
      await this.logAuditTrail({
        user_id: transaction.userId,
        action: 'commission_conversion_failed',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          usdt_amount: transaction.usdtAmount,
          shares_amount: transaction.sharesReceived
        },
        amount: transaction.usdtAmount,
        currency: 'USDT'
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process share transfer between users with atomic operations
   */
  async processShareTransfer(transaction: ShareTransferTransaction): Promise<TransactionResult> {
    const serviceClient = getServiceRoleClient();

    try {
      console.log('🔄 Processing share transfer:', transaction);

      // Validate that sender is not trying to transfer to themselves (except userID 4)
      if (transaction.senderId === transaction.recipientId && transaction.senderId !== 4) {
        throw new Error('You cannot transfer shares to yourself');
      }

      // Get current balances for both users
      const [senderBalance, recipientBalance] = await Promise.all([
        serviceClient.from('commission_balances').select('*').eq('user_id', transaction.senderId).single(),
        serviceClient.from('commission_balances').select('*').eq('user_id', transaction.recipientId).single()
      ]);

      if (senderBalance.error) {
        throw new Error('Failed to get sender balance');
      }

      // Validate sufficient shares
      if (senderBalance.data.share_balance < transaction.totalDeducted) {
        throw new Error('Insufficient shares for transfer including fees');
      }

      // Create transfer transaction record
      const { data: transferRecord, error: transferError } = await serviceClient
        .from('share_transfers')
        .insert({
          sender_user_id: transaction.senderId,
          recipient_user_id: transaction.recipientId,
          shares_transferred: transaction.sharesTransferred,
          transfer_reason: `Share transfer: ${transaction.sharesTransferred} shares`,
          status: 'completed',
          recipient_confirmed: true,
          recipient_confirmed_at: new Date().toISOString(),
          completed_at: new Date().toISOString()
        })
        .select()
        .single();

      if (transferError) {
        throw transferError;
      }

      // Trigger email notification for share transfer
      try {
        const { emailNotificationTriggers } = await import('./emailNotificationTriggers');
        await emailNotificationTriggers.triggerShareTransferNotification(transferRecord.id);
      } catch (emailError) {
        console.warn('Failed to trigger share transfer email notification:', emailError);
        // Don't fail the transfer if email fails
      }

      // Update sender balance
      const newSenderBalance = senderBalance.data.share_balance - transaction.totalDeducted;
      const { error: senderUpdateError } = await serviceClient
        .from('commission_balances')
        .update({
          share_balance: newSenderBalance,
          last_updated: new Date().toISOString()
        })
        .eq('user_id', transaction.senderId);

      if (senderUpdateError) {
        throw senderUpdateError;
      }

      // Update recipient balance (create if doesn't exist)
      let recipientUpdateError;
      let recipientCurrentShares = 0;
      let recipientNewBalance = 0;

      if (recipientBalance.data) {
        // User has existing balance - UPDATE
        recipientCurrentShares = recipientBalance.data.share_balance || 0;
        recipientNewBalance = recipientCurrentShares + transaction.sharesTransferred;
        const { error } = await serviceClient
          .from('commission_balances')
          .update({
            share_balance: recipientNewBalance,
            total_earned_shares: (recipientBalance.data.total_earned_shares || 0) + transaction.sharesTransferred,
            last_updated: new Date().toISOString()
          })
          .eq('user_id', transaction.recipientId);
        recipientUpdateError = error;
      } else {
        // User doesn't have balance record - INSERT
        recipientNewBalance = transaction.sharesTransferred;
        const { error } = await serviceClient
          .from('commission_balances')
          .insert({
            user_id: transaction.recipientId,
            share_balance: recipientNewBalance,
            usdt_balance: 0,
            total_earned_usdt: 0,
            total_earned_shares: transaction.sharesTransferred,
            total_withdrawn: 0,
            escrowed_amount: 0,
            last_updated: new Date().toISOString()
          });
        recipientUpdateError = error;
      }

      if (recipientUpdateError) {
        throw recipientUpdateError;
      }

      // Log audit trail for both users
      await Promise.all([
        this.logAuditTrail({
          user_id: transaction.senderId,
          action: 'share_transfer_sent',
          details: {
            recipient_id: transaction.recipientId,
            shares_transferred: transaction.sharesTransferred,
            transfer_fee: transaction.transferFee,
            transaction_id: transferRecord.id
          },
          amount: transaction.sharesTransferred,
          currency: 'SHARES'
        }),
        this.logAuditTrail({
          user_id: transaction.recipientId,
          action: 'share_transfer_received',
          details: {
            sender_id: transaction.senderId,
            shares_received: transaction.sharesTransferred,
            transaction_id: transferRecord.id
          },
          amount: transaction.sharesTransferred,
          currency: 'SHARES'
        })
      ]);

      console.log('✅ Share transfer completed successfully');

      return {
        success: true,
        transactionId: transferRecord.id,
        data: {
          senderNewBalance: newSenderBalance,
          recipientNewBalance: recipientNewBalance
        }
      };

    } catch (error) {
      console.error('❌ Error processing share transfer:', error);
      
      // Log failed transaction
      await this.logAuditTrail({
        user_id: transaction.senderId,
        action: 'share_transfer_failed',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          recipient_id: transaction.recipientId,
          shares_amount: transaction.sharesTransferred
        },
        amount: transaction.sharesTransferred,
        currency: 'SHARES'
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Process withdrawal request with escrow security
   */
  async processWithdrawalRequest(
    userId: number,
    amount: number,
    walletAddress: string,
    network: string
  ): Promise<{ success: boolean; requestId?: number; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient();

      // Validate KYC status
      const kycValidation = await this.validateKYCForFinancialOperations(userId);
      if (!kycValidation.isValid) {
        return { success: false, error: kycValidation.error };
      }

      // Check minimum withdrawal amount
      if (amount < 10) {
        return { success: false, error: 'Minimum withdrawal amount is $10.00' };
      }

      // 🔒 SECURE ESCROW: Check available balance and create escrow atomically
      console.log(`🔒 [ESCROW] Creating commission withdrawal escrow for user ${userId}, amount: $${amount}`);

      const escrowResult = await createCommissionEscrow(userId, amount, 'withdrawal');

      if (!escrowResult.success) {
        console.error(`❌ [ESCROW] Failed to create withdrawal escrow:`, escrowResult.error);

        if (escrowResult.error?.includes('Insufficient available balance')) {
          // Get current balance info for detailed error message
          const balanceInfo = await getAvailableCommissionBalance(userId);
          return {
            success: false,
            error: `Insufficient available balance. Total: $${balanceInfo.totalBalance.toFixed(2)}, Escrowed: $${balanceInfo.escrowedAmount.toFixed(2)}, Available: $${balanceInfo.availableBalance.toFixed(2)}`
          };
        } else if (escrowResult.error?.includes('concurrent_request')) {
          return {
            success: false,
            error: 'Another commission request is currently being processed. Please wait and try again.'
          };
        } else {
          return { success: false, error: escrowResult.error || 'Failed to secure funds for withdrawal' };
        }
      }

      // Create withdrawal request (escrow already secured)
      const { data: withdrawal, error: withdrawalError } = await serviceClient
        .from('commission_withdrawals')
        .insert({
          user_id: userId,
          amount,
          wallet_address: walletAddress,
          network,
          status: 'pending',
          withdrawal_type: 'usdt',
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (withdrawalError) {
        console.error('Error creating withdrawal request:', withdrawalError);

        // 🔒 ROLLBACK: Release escrow if withdrawal creation failed
        console.log(`🔒 [ESCROW] Rolling back escrow due to withdrawal creation failure`);
        await releaseCommissionEscrow(userId, amount);

        return { success: false, error: 'Failed to create withdrawal request' };
      }

      // Send notification email
      await affiliateNotificationService.sendWithdrawalRequestNotification(
        userId,
        amount,
        walletAddress,
        network
      );

      // Log audit trail
      await this.logAuditTrail({
        user_id: userId,
        action: 'withdrawal_request_created',
        details: {
          amount,
          wallet_address: walletAddress,
          network,
          withdrawal_id: withdrawal.id
        },
        amount,
        currency: 'USDT'
      });

      return { success: true, requestId: withdrawal.id };
    } catch (error) {
      console.error('Error processing withdrawal request:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Validate KYC status for financial operations
   */
  private async validateKYCForFinancialOperations(userId: number): Promise<{ isValid: boolean; error?: string }> {
    try {
      const serviceClient = getServiceRoleClient();

      const { data: kycInfo, error } = await serviceClient
        .from('kyc_information')
        .select('kyc_status, verification_status')
        .eq('user_id', userId)
        .single();

      if (error || !kycInfo) {
        return { isValid: false, error: 'KYC verification required. Please complete your KYC verification first.' };
      }

      const status = kycInfo.kyc_status || kycInfo.verification_status;
      if (status !== 'approved' && status !== 'completed' && status !== 'verified') {
        return { isValid: false, error: 'KYC verification must be completed and approved before financial operations.' };
      }

      return { isValid: true };
    } catch (error) {
      console.error('Error validating KYC status:', error);
      return { isValid: false, error: 'Unable to verify KYC status. Please try again.' };
    }
  }

  /**
   * Log audit trail for financial transactions
   */
  private async logAuditTrail(auditData: {
    user_id: number;
    action: string;
    details: any;
    amount: number;
    currency: string;
  }): Promise<void> {
    try {
      const serviceClient = getServiceRoleClient();
      await serviceClient
        .from('financial_audit_log')
        .insert({
          activity_type: auditData.action,
          section_name: 'affiliate_transactions',
          amount: auditData.amount,
          admin_id: auditData.user_id,
          metadata: {
            currency: auditData.currency,
            details: auditData.details,
            timestamp: new Date().toISOString(),
            user_id: auditData.user_id
          }
        });
    } catch (error) {
      console.error('❌ Error logging audit trail:', error);
      // Don't throw - audit logging failure shouldn't break the main transaction
    }
  }
}

export const affiliateTransactionService = new AffiliateTransactionService();

// Export escrow functions for use in other components
export {
  createCommissionEscrow,
  releaseCommissionEscrow,
  getAvailableCommissionBalance,
  validateSponsorAssignment
};
