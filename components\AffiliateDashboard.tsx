import React, { useState, useEffect } from 'react';
import { supabase, getServiceRoleClient } from '../lib/supabase';
import { DashboardSwitcher } from './DashboardSwitcher';
import { ReferralCenter } from './referrals/ReferralCenter';
import { NotificationCenter } from './user/NotificationCenter';
import { USDTToSharesConverter } from './affiliate/USDTToSharesConverter';
import { CommissionWithdrawal } from './user/CommissionWithdrawal';
import { ShareTransferSystem } from './affiliate/ShareTransferSystem';
import { ComprehensiveAffiliateManager } from './affiliate/ComprehensiveAffiliateManager';
import { AffiliateTrainingCenter } from './affiliate/AffiliateTrainingCenter';
import { MarketingToolsManager } from './affiliate/MarketingToolsManager';
import { getFullVersion } from '../lib/version';

interface AffiliateDashboardProps {
  user: any;
  onLogout: () => void;
  onSwitchDashboard: (dashboard: 'shareholder' | 'affiliate') => void;
}

type AffiliateSection = 
  | 'overview'
  | 'referrals' 
  | 'commissions'
  | 'marketing-tools'
  | 'team'
  | 'training'
  | 'notifications'
  | 'settings';

const affiliateNavigation = [
  {
    id: 'overview' as AffiliateSection,
    label: 'Overview',
    icon: '📊',
    description: 'Commission summary and performance'
  },
  {
    id: 'referrals' as AffiliateSection,
    label: 'Referrals',
    icon: '🤝',
    description: 'Manage referral links and track conversions'
  },
  {
    id: 'commissions' as AffiliateSection,
    label: 'Commissions',
    icon: '💰',
    description: 'Track USDT and share commissions'
  },
  {
    id: 'marketing-tools' as AffiliateSection,
    label: 'Marketing Tools',
    icon: '🎯',
    description: 'Banners, links, and promotional materials'
  },
  {
    id: 'team' as AffiliateSection,
    label: 'My Team',
    icon: '👥',
    description: 'View your referral network'
  },
  {
    id: 'training' as AffiliateSection,
    label: 'Training',
    icon: '🎓',
    description: 'Learn effective networking strategies'
  },
  {
    id: 'notifications' as AffiliateSection,
    label: 'Notifications',
    icon: '🔔',
    description: 'Commission alerts and updates'
  },
  {
    id: 'settings' as AffiliateSection,
    label: 'Settings',
    icon: '⚙️',
    description: 'Account preferences'
  }
];

export const AffiliateDashboard: React.FC<AffiliateDashboardProps> = ({
  user,
  onLogout,
  onSwitchDashboard
}) => {
  const [activeSection, setActiveSection] = useState<AffiliateSection>('overview');
  const [commissionData, setCommissionData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    loadCommissionData();
  }, [user]);

  const loadCommissionData = async () => {
    if (!user?.database_user?.id) return;

    try {
      const serviceClient = getServiceRoleClient();

      // Load commission balances
      const { data: commissions } = await serviceClient
        .from('commission_balances')
        .select('*')
        .eq('user_id', user.database_user.id)
        .single();

      // Load referral count
      const { data: referrals } = await serviceClient
        .from('referrals')
        .select('id')
        .eq('referrer_id', user.database_user.id);

      // Load approved commission conversions (USDT converted to shares)
      const { data: conversions } = await serviceClient
        .from('commission_conversions')
        .select('shares_requested, usdt_amount, status')
        .eq('user_id', user.database_user.id)
        .eq('status', 'approved');

      // Calculate total converted shares
      const totalConvertedShares = conversions?.reduce((sum, conversion) => sum + conversion.shares_requested, 0) || 0;
      const totalUsdtConverted = conversions?.reduce((sum, conversion) => sum + parseFloat(conversion.usdt_amount), 0) || 0;

      // Calculate total shares owned (commission shares + converted shares)
      const commissionShares = commissions?.share_balance || 0;
      const totalSharesOwned = commissionShares + totalConvertedShares;

      setCommissionData({
        ...commissions,
        referral_count: referrals?.length || 0,
        // Add conversion data
        total_converted_shares: totalConvertedShares,
        total_usdt_converted: totalUsdtConverted,
        // Update share balance to include conversions
        total_shares_owned: totalSharesOwned,
        commission_shares_only: commissionShares
      });
    } catch (error) {
      console.error('Error loading commission data:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-xl p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">
          Welcome to Your Affiliate Dashboard! 🤝
        </h1>
        <p className="text-blue-100 mb-4">
          Build your network, earn commissions, and grow your income with Aureus Alliance Holdings.
        </p>
        <div className="bg-white/10 rounded-lg p-3">
          <p className="text-sm">
            💡 <strong>Pro Tip:</strong> Want to invest in shares? Switch to the Shareholder Dashboard using the switcher above!
          </p>
        </div>
      </div>

      {/* 1. USDT Commission Balance Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center gap-3 mb-4">
          <span className="text-3xl">💰</span>
          <div>
            <h2 className="text-xl font-bold text-white">USDT Commission Earnings</h2>
            <p className="text-gray-400 text-sm">Cash earnings from referral commissions available for withdrawal or conversion to shares</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">
              ${commissionData?.usdt_balance?.toFixed(2) || '0.00'}
            </div>
            <div className="text-gray-300 text-sm">Current Balance</div>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-400 mb-1">
              ${commissionData?.total_earned_usdt?.toFixed(2) || '0.00'}
            </div>
            <div className="text-gray-300 text-sm">Total Earned</div>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-400 mb-1">
              ${commissionData?.usdt_balance?.toFixed(2) || '0.00'}
            </div>
            <div className="text-gray-300 text-sm">Available for Withdrawal</div>
          </div>
        </div>

        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setActiveSection('commissions')}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
          >
            Withdraw USDT
          </button>
          <button
            onClick={() => setActiveSection('commissions')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
          >
            Convert to Shares
          </button>
        </div>
      </div>

      {/* 2. Presale Shares Received Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center gap-3 mb-4">
          <span className="text-3xl">🎁</span>
          <div>
            <h2 className="text-xl font-bold text-white">Bonus Shares from Referrals</h2>
            <p className="text-gray-400 text-sm">Free shares awarded through the referral program (15% commission automatically converted to shares)</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-400 mb-1">
              {commissionData?.commission_shares_only?.toFixed(2) || '0.00'}
            </div>
            <div className="text-gray-300 text-sm">Presale Shares Received</div>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">
              ${((commissionData?.commission_shares_only || 0) * 5).toFixed(2)}
            </div>
            <div className="text-gray-300 text-sm">Current Value</div>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-400 mb-1">
              ${((commissionData?.commission_shares_only || 0) * 0.5).toFixed(2)}
            </div>
            <div className="text-gray-300 text-sm">Annual Dividend Projection</div>
          </div>
        </div>

        <div className="bg-blue-900/30 border border-blue-500/50 rounded-lg p-4">
          <p className="text-blue-200 text-sm">
            <strong>💎 Full Dividend Rights:</strong> These shares were earned through your referral activity and receive full dividend rights when mining operations begin.
          </p>
        </div>
      </div>
      {/* 3. USDT-to-Shares Conversions Section */}
      <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
        <div className="flex items-center gap-3 mb-4">
          <span className="text-3xl">🔄</span>
          <div>
            <h2 className="text-xl font-bold text-white">Shares from Commission Conversions</h2>
            <p className="text-gray-400 text-sm">Shares purchased by converting your USDT commission balance to shareholdings</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-400 mb-1">
              {commissionData?.total_converted_shares?.toFixed(2) || '0.00'}
            </div>
            <div className="text-gray-300 text-sm">Total Converted Shares</div>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">
              ${commissionData?.total_usdt_converted?.toFixed(2) || '0.00'}
            </div>
            <div className="text-gray-300 text-sm">USDT Converted</div>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-400 mb-1">
              ${(commissionData?.total_usdt_converted && commissionData?.total_converted_shares ?
                (commissionData.total_usdt_converted / commissionData.total_converted_shares).toFixed(2) : '0.00')}
            </div>
            <div className="text-gray-300 text-sm">Average Conversion Rate</div>
          </div>
        </div>

        <button
          onClick={() => setActiveSection('commissions')}
          className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors font-medium"
        >
          View Conversion History
        </button>
      </div>

      {/* 4. Total Shares Owned Section */}
      <div className="bg-gray-800 rounded-lg border-2 border-yellow-500/50 p-6">
        <div className="flex items-center gap-3 mb-4">
          <span className="text-3xl">🏆</span>
          <div>
            <h2 className="text-xl font-bold text-white">Complete Share Portfolio</h2>
            <p className="text-gray-400 text-sm">Your total shareholding including purchased shares, presale shares, and converted shares</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gradient-to-br from-yellow-600/20 to-yellow-800/20 border border-yellow-500/30 rounded-lg p-4 text-center">
            <div className="text-3xl font-bold text-yellow-400 mb-1">
              {commissionData?.total_shares_owned?.toFixed(2) || '0.00'}
            </div>
            <div className="text-yellow-200 text-sm font-medium">Grand Total Shares</div>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">
              ${((commissionData?.total_shares_owned || 0) * 5).toFixed(2)}
            </div>
            <div className="text-gray-300 text-sm">Current Portfolio Value</div>
          </div>
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-400 mb-1">
              ${((commissionData?.total_shares_owned || 0) * 0.5).toFixed(2)}
            </div>
            <div className="text-gray-300 text-sm">Projected Annual Dividends</div>
          </div>
        </div>

        {/* Portfolio Composition Breakdown */}
        <div className="bg-gray-700/30 rounded-lg p-4">
          <h3 className="text-white font-medium mb-3">📊 Portfolio Composition</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-300 text-sm">🎁 Presale Shares (Referral Bonus)</span>
              <span className="text-yellow-400 font-medium">{commissionData?.commission_shares_only?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-300 text-sm">🔄 Converted Shares (USDT → Shares)</span>
              <span className="text-blue-400 font-medium">{commissionData?.total_converted_shares?.toFixed(2) || '0.00'}</span>
            </div>
            <div className="border-t border-gray-600 pt-2 mt-2">
              <div className="flex justify-between items-center">
                <span className="text-white font-medium">🏆 Total Portfolio</span>
                <span className="text-yellow-400 font-bold text-lg">{commissionData?.total_shares_owned?.toFixed(2) || '0.00'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Referral Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center gap-3 mb-4">
            <span className="text-3xl">👥</span>
            <div>
              <h3 className="text-lg font-bold text-white">Referral Network</h3>
              <p className="text-gray-400 text-sm">People you've referred to the platform</p>
            </div>
          </div>
          <div className="text-3xl font-bold text-blue-400 mb-2">
            {commissionData?.referral_count || 0}
          </div>
          <div className="text-gray-300 text-sm">Total Referrals</div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
          <div className="flex items-center gap-3 mb-4">
            <span className="text-3xl">💎</span>
            <div>
              <h3 className="text-lg font-bold text-white">Lifetime Earnings</h3>
              <p className="text-gray-400 text-sm">Combined value of all earnings</p>
            </div>
          </div>
          <div className="text-3xl font-bold text-purple-400 mb-2">
            ${((commissionData?.total_earned_usdt || 0) + (commissionData?.total_shares_owned || 0) * 5).toFixed(2)}
          </div>
          <div className="text-gray-300 text-sm">USDT + Share Value</div>
        </div>
      </div>

      {/* Important Commission Policy Notice */}
      <div className="bg-gradient-to-r from-orange-900/40 to-red-900/40 border border-orange-500/50 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <div className="text-2xl">⚠️</div>
          <div className="flex-1">
            <h3 className="text-lg font-bold text-orange-200 mb-2">
              Important: Share Commission Policy
            </h3>
            <div className="space-y-2 text-sm text-orange-100">
              <p>
                <strong className="text-orange-200">Share commissions are exclusive to the presale period only.</strong>
                Once the 200,000 presale shares are sold out, share commissions will permanently end.
              </p>
              <p>
                <strong className="text-orange-200">USDT commissions will continue indefinitely</strong> for all future
                share purchases across all 19 phases, but share rewards are limited to presale only.
              </p>
              <div className="bg-orange-800/30 rounded-md p-3 mt-3">
                <p className="text-xs text-orange-200">
                  💡 <strong>Strategy Tip:</strong> Maximize your share commission earnings now during the presale period.
                  After presale, you'll only earn USDT commissions on referrals.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h2 className="text-xl font-bold text-white mb-4">🚀 Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveSection('referrals')}
            className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg transition-colors text-left hover:transform hover:scale-105"
          >
            <div className="text-2xl mb-2">🔗</div>
            <div className="font-semibold">Get Referral Link</div>
            <div className="text-sm text-blue-100">Share and earn commissions</div>
          </button>

          <button
            onClick={() => setActiveSection('commissions')}
            className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg transition-colors text-left hover:transform hover:scale-105"
          >
            <div className="text-2xl mb-2">💰</div>
            <div className="font-semibold">Manage Commissions</div>
            <div className="text-sm text-green-100">Withdraw or convert earnings</div>
          </button>

          <button
            onClick={() => setActiveSection('marketing-tools')}
            className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg transition-colors text-left hover:transform hover:scale-105"
          >
            <div className="text-2xl mb-2">🎯</div>
            <div className="font-semibold">Marketing Materials</div>
            <div className="text-sm text-purple-100">Download banners and content</div>
          </button>

          <button
            onClick={() => onSwitchDashboard('shareholder')}
            className="bg-yellow-600 hover:bg-yellow-700 text-white p-4 rounded-lg transition-colors text-left hover:transform hover:scale-105"
          >
            <div className="text-2xl mb-2">📈</div>
            <div className="font-semibold">Buy Shares</div>
            <div className="text-sm text-yellow-100">Switch to investor dashboard</div>
          </button>
        </div>
      </div>
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'overview':
        return renderOverview();
      case 'referrals':
        return (
          <ReferralCenter
            userId={user?.database_user?.id || 0}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        );
      case 'notifications':
        return (
          <NotificationCenter
            userId={user?.database_user?.id || 0}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        );
      case 'commissions':
        return (
          <ComprehensiveAffiliateManager
            userId={user?.database_user?.id || 0}
            onDataRefresh={loadCommissionData}
          />
        );
      case 'marketing-tools':
        return (
          <MarketingToolsManager
            userId={user?.database_user?.id || 0}
            affiliateData={{
              user: user?.database_user,
              referralCount: commissionData?.referral_count || 0,
              commissionBalance: commissionData
            }}
          />
        );
      case 'team':
        return (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">👥 My Team</h2>
            <p className="text-gray-400">Team management features coming soon...</p>
          </div>
        );
      case 'training':
        return (
          <AffiliateTrainingCenter
            userId={user?.database_user?.id || 0}
            className="bg-gray-800 rounded-lg border border-gray-700 p-6"
          />
        );
      case 'settings':
        return (
          <div className="bg-gray-800 rounded-lg border border-gray-700 p-6">
            <h2 className="text-2xl font-bold text-white mb-4">⚙️ Settings</h2>
            <p className="text-gray-400">Affiliate settings coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 flex">
      {/* Sidebar */}
      <div className={`${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 fixed lg:static inset-y-0 left-0 z-50 w-64 bg-gray-800 border-r border-gray-700 transition-transform duration-300 ease-in-out`}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-700">
            <DashboardSwitcher
              currentDashboard="affiliate"
              onSwitch={onSwitchDashboard}
              user={user}
            />
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {affiliateNavigation.map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  setActiveSection(item.id);
                  setIsMobileMenuOpen(false);
                }}
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeSection === item.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <span className="text-xl">{item.icon}</span>
                <div>
                  <div className="font-medium">{item.label}</div>
                  <div className="text-xs opacity-75">{item.description}</div>
                </div>
              </button>
            ))}
          </nav>

          {/* User Info */}
          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {(user?.database_user?.full_name || user?.email)?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="text-white text-sm font-medium">
                  {user?.database_user?.full_name || user?.email?.split('@')[0]}
                </p>
                <p className="text-gray-400 text-xs">Affiliate</p>
              </div>
            </div>
            <button
              onClick={onLogout}
              className="w-full bg-gray-700 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 lg:ml-0">
        {/* Mobile Header */}
        <div className="lg:hidden bg-gray-800 border-b border-gray-700 p-4">
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="text-white"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {renderSectionContent()}
        </div>

        {/* Version Footer */}
        <div className="border-t border-gray-700 p-4 text-center">
          <p className="text-gray-500 text-xs">
            Aureus Africa {getFullVersion()} • {new Date().getFullYear()} Aureus Alliance Holdings (Pty) Ltd
          </p>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </div>
  );
};
