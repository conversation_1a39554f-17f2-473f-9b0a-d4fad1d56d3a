/**
 * PORTFOLIO CALCULATIONS HOOK
 * 
 * Handles complex portfolio calculations including dividend projections,
 * mining calculations, and performance analytics.
 */

import { useMemo } from 'react';
import { ShareHolding, PortfolioSummary } from './usePortfolioData';

// Mining constants
const TOTAL_SHARES = 1400000;
const PLANT_CAPACITY_TPH = 200;
const EFFECTIVE_HOURS_PER_DAY = 20;
const OPERATING_DAYS_PER_YEAR = 330;
const BULK_DENSITY_T_PER_M3 = 1.8;
const HA_PER_PLANT = 25;

const DEFAULT_MINING_PARAMS = {
  avgGravelThickness: 0.8,
  inSituGrade: 0.9,
  recoveryFactor: 70,
  goldPriceUsdPerKg: 109026,
  opexPercent: 45
};

export interface MiningCalculations {
  annualTonnesProcessed: number;
  annualGoldProduced: number;
  annualRevenue: number;
  annualProfit: number;
  dividendPerShare: number;
  userAnnualDividend: number;
  userMonthlyDividend: number;
  yieldPercentage: number;
}

export interface PerformanceMetrics {
  totalReturn: number;
  totalReturnPercent: number;
  annualizedReturn: number;
  bestPerformingPhase: string;
  worstPerformingPhase: string;
  averageHoldingPeriod: number;
}

export interface UsePortfolioCalculationsReturn {
  miningCalculations: MiningCalculations;
  performanceMetrics: PerformanceMetrics;
  phaseBreakdown: Array<{
    phaseName: string;
    shares: number;
    invested: number;
    currentValue: number;
    gainLoss: number;
    gainLossPercent: number;
  }>;
  monthlyProjections: Array<{
    month: string;
    projectedDividend: number;
    cumulativeDividend: number;
  }>;
}

export const usePortfolioCalculations = (
  holdings: ShareHolding[],
  summary: PortfolioSummary,
  currentPhase: any,
  numberOfPlants: number = 10
): UsePortfolioCalculationsReturn => {
  const miningCalculations = useMemo((): MiningCalculations => {
    // Calculate annual production capacity
    const annualTonnesProcessed = numberOfPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
    
    // Calculate gold production
    const annualGoldProduced = annualTonnesProcessed * (DEFAULT_MINING_PARAMS.inSituGrade / 1000) * (DEFAULT_MINING_PARAMS.recoveryFactor / 100);
    
    // Calculate revenue and profit
    const annualRevenue = annualGoldProduced * DEFAULT_MINING_PARAMS.goldPriceUsdPerKg;
    const annualProfit = annualRevenue * (1 - DEFAULT_MINING_PARAMS.opexPercent / 100);
    
    // Calculate dividends
    const dividendPerShare = annualProfit / TOTAL_SHARES;
    const userAnnualDividend = summary.totalShares * dividendPerShare;
    const userMonthlyDividend = userAnnualDividend / 12;
    
    // Calculate yield
    const yieldPercentage = summary.totalInvested > 0 ? (userAnnualDividend / summary.totalInvested) * 100 : 0;

    return {
      annualTonnesProcessed,
      annualGoldProduced,
      annualRevenue,
      annualProfit,
      dividendPerShare,
      userAnnualDividend,
      userMonthlyDividend,
      yieldPercentage
    };
  }, [summary.totalShares, summary.totalInvested, numberOfPlants]);

  const performanceMetrics = useMemo((): PerformanceMetrics => {
    if (holdings.length === 0) {
      return {
        totalReturn: 0,
        totalReturnPercent: 0,
        annualizedReturn: 0,
        bestPerformingPhase: 'N/A',
        worstPerformingPhase: 'N/A',
        averageHoldingPeriod: 0
      };
    }

    const totalReturn = summary.unrealizedGains;
    const totalReturnPercent = summary.unrealizedGainsPercent;

    // Calculate annualized return (simplified)
    const oldestHolding = holdings.reduce((oldest, holding) => {
      const holdingDate = new Date(holding.purchase_date);
      const oldestDate = new Date(oldest.purchase_date);
      return holdingDate < oldestDate ? holding : oldest;
    });

    const holdingPeriodYears = (Date.now() - new Date(oldestHolding.purchase_date).getTime()) / (365.25 * 24 * 60 * 60 * 1000);
    const annualizedReturn = holdingPeriodYears > 0 ? Math.pow(1 + totalReturnPercent / 100, 1 / holdingPeriodYears) - 1 : 0;

    // Find best and worst performing phases
    const phasePerformance = holdings.reduce((acc, holding) => {
      const currentValue = holding.shares_purchased * (currentPhase?.price_per_share || 5.00);
      const gainLoss = currentValue - holding.total_amount;
      const gainLossPercent = (gainLoss / holding.total_amount) * 100;

      if (!acc[holding.phase_name]) {
        acc[holding.phase_name] = { gainLossPercent, phaseName: holding.phase_name };
      } else {
        acc[holding.phase_name].gainLossPercent = (acc[holding.phase_name].gainLossPercent + gainLossPercent) / 2;
      }

      return acc;
    }, {} as Record<string, { gainLossPercent: number; phaseName: string }>);

    const phasePerformanceArray = Object.values(phasePerformance);
    const bestPerformingPhase = phasePerformanceArray.length > 0 
      ? phasePerformanceArray.reduce((best, phase) => phase.gainLossPercent > best.gainLossPercent ? phase : best).phaseName
      : 'N/A';
    const worstPerformingPhase = phasePerformanceArray.length > 0
      ? phasePerformanceArray.reduce((worst, phase) => phase.gainLossPercent < worst.gainLossPercent ? phase : worst).phaseName
      : 'N/A';

    // Calculate average holding period
    const totalHoldingDays = holdings.reduce((sum, holding) => {
      const holdingDays = (Date.now() - new Date(holding.purchase_date).getTime()) / (24 * 60 * 60 * 1000);
      return sum + holdingDays;
    }, 0);
    const averageHoldingPeriod = totalHoldingDays / holdings.length;

    return {
      totalReturn,
      totalReturnPercent,
      annualizedReturn: annualizedReturn * 100,
      bestPerformingPhase,
      worstPerformingPhase,
      averageHoldingPeriod
    };
  }, [holdings, summary, currentPhase]);

  const phaseBreakdown = useMemo(() => {
    const breakdown = holdings.reduce((acc, holding) => {
      const phaseName = holding.phase_name;
      const currentValue = holding.shares_purchased * (currentPhase?.price_per_share || 5.00);
      const gainLoss = currentValue - holding.total_amount;
      const gainLossPercent = (gainLoss / holding.total_amount) * 100;

      if (!acc[phaseName]) {
        acc[phaseName] = {
          phaseName,
          shares: 0,
          invested: 0,
          currentValue: 0,
          gainLoss: 0,
          gainLossPercent: 0
        };
      }

      acc[phaseName].shares += holding.shares_purchased;
      acc[phaseName].invested += holding.total_amount;
      acc[phaseName].currentValue += currentValue;
      acc[phaseName].gainLoss += gainLoss;

      return acc;
    }, {} as Record<string, any>);

    // Calculate weighted average gain/loss percentage for each phase
    Object.values(breakdown).forEach((phase: any) => {
      phase.gainLossPercent = phase.invested > 0 ? (phase.gainLoss / phase.invested) * 100 : 0;
    });

    return Object.values(breakdown);
  }, [holdings, currentPhase]);

  const monthlyProjections = useMemo(() => {
    const projections = [];
    const monthlyDividend = miningCalculations.userMonthlyDividend;
    let cumulativeDividend = 0;

    for (let i = 1; i <= 12; i++) {
      cumulativeDividend += monthlyDividend;
      const date = new Date();
      date.setMonth(date.getMonth() + i);
      
      projections.push({
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        projectedDividend: monthlyDividend,
        cumulativeDividend
      });
    }

    return projections;
  }, [miningCalculations.userMonthlyDividend]);

  return {
    miningCalculations,
    performanceMetrics,
    phaseBreakdown,
    monthlyProjections
  };
};
