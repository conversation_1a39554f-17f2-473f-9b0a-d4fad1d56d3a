import React, { useState } from 'react';

interface FinancialDataPageProps {
  onNavigate: (page: string) => void;
}

const FinancialDataPage: React.FC<FinancialDataPageProps> = ({ onNavigate }) => {
  const [activeTable, setActiveTable] = useState<string>('production');

  // 5-Year Production & Dividend Data
  const productionData = [
    { 
      year: "2026", 
      plants: 10, 
      hectares: 250, 
      goldKg: 875, 
      revenue: "$95.4M", 
      ebit: "$52.5M",
      dividendPerShare: "$37.50",
      totalDividends: "$52.5M"
    },
    { 
      year: "2027", 
      plants: 25, 
      hectares: 625, 
      goldKg: 2188, 
      revenue: "$238.5M", 
      ebit: "$131.2M",
      dividendPerShare: "$93.75",
      totalDividends: "$131.2M"
    },
    { 
      year: "2028", 
      plants: 50, 
      hectares: 1250, 
      goldKg: 4375, 
      revenue: "$477M", 
      ebit: "$262.4M",
      dividendPerShare: "$187.50",
      totalDividends: "$262.4M"
    },
    { 
      year: "2029", 
      plants: 100, 
      hectares: 2500, 
      goldKg: 8750, 
      revenue: "$954M", 
      ebit: "$524.7M",
      dividendPerShare: "$375.00",
      totalDividends: "$524.7M"
    },
    { 
      year: "2030", 
      plants: 200, 
      hectares: 6000, 
      goldKg: 21000, 
      revenue: "$2.29B", 
      ebit: "$1.26B",
      dividendPerShare: "$900.00",
      totalDividends: "$1.26B"
    }
  ];

  // Phase Breakdown Data
  const phaseBreakdown = [
    { 
      phase: "Presale", 
      price: "$5.00", 
      shares: "200,000", 
      totalFunds: "$1,000,000",
      operations: "Launch First Wash Plant at Kadoma",
      csr: "Feed 2,000+ Children, Install 4 Boreholes"
    },
    { 
      phase: "Phase 1-3", 
      price: "$10-$20", 
      shares: "375,000", 
      totalFunds: "$5,625,000",
      operations: "Complete 3 Wash Plants, Begin Production",
      csr: "Feed 10,000+ Children, Build 6 Classrooms"
    },
    { 
      phase: "Phase 4-6", 
      price: "$25-$35", 
      shares: "175,000", 
      totalFunds: "$5,250,000",
      operations: "Launch 6th Plant, Expand Operations",
      csr: "Feed 15,000+ Children, Mobile Health Units"
    },
    { 
      phase: "Phase 7-10", 
      price: "$40-$100", 
      shares: "200,000", 
      totalFunds: "$14,000,000",
      operations: "10 Plants Total, Begin Mutare Expansion",
      csr: "Feed 22,000+ Children, Hospital Development"
    },
    { 
      phase: "Phase 11-15", 
      price: "$200-$600", 
      shares: "250,000", 
      totalFunds: "$100,000,000",
      operations: "87 Plants, Multi-Site Operations",
      csr: "Feed 150,000+ Children, Regional Hospital"
    },
    { 
      phase: "Phase 16-19", 
      price: "$700-$1,000", 
      shares: "200,000", 
      totalFunds: "$170,000,000",
      operations: "257 Plants, Multi-Country Expansion",
      csr: "Feed 250,000+ Children, Complete Hospital"
    }
  ];

  // Operational Metrics
  const operationalMetrics = [
    { metric: "Plant Capacity", value: "200 TPH", description: "Tonnes per hour processing capacity per plant" },
    { metric: "Operating Hours", value: "20 hours/day", description: "Daily operational hours per plant" },
    { metric: "Operating Days", value: "330 days/year", description: "Annual operating days accounting for maintenance" },
    { metric: "Land per Plant", value: "25 hectares", description: "Land area covered by each wash plant" },
    { metric: "In-Situ Grade", value: "0.9 g/m³", description: "Gold content in the gravel deposits" },
    { metric: "Recovery Rate", value: "70% (target 95%)", description: "Percentage of gold successfully extracted" },
    { metric: "Bulk Density", value: "1.8 T/m³", description: "Weight of gravel per cubic meter" },
    { metric: "Operating Costs", value: "45% of revenue", description: "Total operational expenses as percentage of revenue" }
  ];

  return (
    <div className="page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <div className="breadcrumb">
            <button onClick={() => onNavigate('home')} className="breadcrumb-link">
              Home
            </button>
            <span className="breadcrumb-separator">→</span>
            <span className="breadcrumb-current">Financial Data</span>
          </div>
          
          <h1 className="page-title">Financial Data & Projections</h1>
          <p className="page-subtitle">
            Comprehensive financial tables, production forecasts, and operational metrics 
            based on proven mining parameters and conservative projections
          </p>
        </div>
      </section>

      {/* Data Navigation */}
      <section className="data-navigation">
        <div className="container">
          <div className="nav-tabs">
            <button 
              className={`nav-tab ${activeTable === 'production' ? 'active' : ''}`}
              onClick={() => setActiveTable('production')}
            >
              📊 Production & Dividends
            </button>
            <button 
              className={`nav-tab ${activeTable === 'phases' ? 'active' : ''}`}
              onClick={() => setActiveTable('phases')}
            >
              📈 Phase Breakdown
            </button>
            <button 
              className={`nav-tab ${activeTable === 'operations' ? 'active' : ''}`}
              onClick={() => setActiveTable('operations')}
            >
              ⚙️ Operational Metrics
            </button>
          </div>
        </div>
      </section>

      {/* Production & Dividends Table */}
      {activeTable === 'production' && (
        <section className="data-section">
          <div className="container">
            <div className="section-header">
              <h2>5-Year Production & Dividend Projections</h2>
              <p>Gold output and dividend forecasts based on expansion timeline</p>
            </div>

            <div className="table-wrapper">
              <div className="data-table">
                <table>
                  <thead>
                    <tr>
                      <th>Year</th>
                      <th>Plants</th>
                      <th>Hectares</th>
                      <th>Gold Output (kg)</th>
                      <th>Revenue</th>
                      <th>EBIT</th>
                      <th>Dividend/Share</th>
                      <th>Total Dividends</th>
                    </tr>
                  </thead>
                  <tbody>
                    {productionData.map((row, index) => (
                      <tr key={index}>
                        <td className="year-cell"><strong>{row.year}</strong></td>
                        <td>{row.plants}</td>
                        <td>{row.hectares.toLocaleString()}</td>
                        <td>{row.goldKg.toLocaleString()}</td>
                        <td>{row.revenue}</td>
                        <td>{row.ebit}</td>
                        <td className="highlight">{row.dividendPerShare}</td>
                        <td>{row.totalDividends}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Enhanced Key Assumptions Section */}
            <div className="enhanced-assumptions-section">
              <div className="assumptions-header">
                <div className="assumptions-icon">📊</div>
                <div className="assumptions-content">
                  <h3>Key Financial Assumptions</h3>
                  <p>Conservative estimates based on industry standards and current market conditions</p>
                </div>
              </div>

              <div className="assumptions-grid">
                <div className="assumption-card">
                  <div className="assumption-icon">🥇</div>
                  <div className="assumption-details">
                    <h4>Gold Price</h4>
                    <div className="assumption-value">$109,026 per kg</div>
                    <p>Current market rate (London Bullion Market)</p>
                  </div>
                </div>

                <div className="assumption-card">
                  <div className="assumption-icon">⚙️</div>
                  <div className="assumption-details">
                    <h4>Recovery Rate</h4>
                    <div className="assumption-value">70%</div>
                    <p>Conservative estimate, targeting 95% efficiency</p>
                  </div>
                </div>

                <div className="assumption-card">
                  <div className="assumption-icon">💰</div>
                  <div className="assumption-details">
                    <h4>Operating Costs</h4>
                    <div className="assumption-value">45%</div>
                    <p>Of revenue (industry standard benchmark)</p>
                  </div>
                </div>

                <div className="assumption-card">
                  <div className="assumption-icon">📈</div>
                  <div className="assumption-details">
                    <h4>Dividend Payout</h4>
                    <div className="assumption-value">100%</div>
                    <p>Of EBIT distributed to shareholders</p>
                  </div>
                </div>

                <div className="assumption-card">
                  <div className="assumption-icon">🎯</div>
                  <div className="assumption-details">
                    <h4>Total Shares</h4>
                    <div className="assumption-value">1.4M</div>
                    <p>Across all 20 phases (Pre-sale + Phase 1-19)</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Phase Breakdown Table */}
      {activeTable === 'phases' && (
        <section className="data-section">
          <div className="container">
            <div className="section-header">
              <h2>Share Phase Fund Allocation</h2>
              <p>Detailed breakdown of how funds are allocated across operations and CSR</p>
            </div>

            <div className="table-wrapper">
              <div className="data-table">
                <table>
                  <thead>
                    <tr>
                      <th>Phase Group</th>
                      <th>Price Range</th>
                      <th>Total Shares</th>
                      <th>Total Funds</th>
                      <th>Operations Focus</th>
                      <th>CSR Impact</th>
                    </tr>
                  </thead>
                  <tbody>
                    {phaseBreakdown.map((row, index) => (
                      <tr key={index}>
                        <td className="phase-cell"><strong>{row.phase}</strong></td>
                        <td>{row.price}</td>
                        <td>{row.shares}</td>
                        <td className="highlight">{row.totalFunds}</td>
                        <td>{row.operations}</td>
                        <td>{row.csr}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Enhanced Phase Summary Section */}
            <div className="enhanced-phase-summary">
              <div className="summary-header">
                <div className="summary-icon">💰</div>
                <div className="summary-content">
                  <h3>Fund Allocation Overview</h3>
                  <p>Strategic distribution of shareholder funds across operational priorities</p>
                </div>
              </div>

              <div className="enhanced-summary-cards">
                <div className="enhanced-summary-card funding-capacity">
                  <div className="card-header">
                    <div className="card-icon">🎯</div>
                    <div className="card-badge">Total Capacity</div>
                  </div>
                  <div className="card-content">
                    <h4>Total Funding Capacity</h4>
                    <div className="enhanced-summary-value">$295.875M</div>
                    <p>Across all 20 phases when fully subscribed</p>
                    <div className="progress-indicator">
                      <div className="progress-bar">
                        <div className="progress-fill" style={{width: '100%'}}></div>
                      </div>
                      <span className="progress-text">Maximum Potential</span>
                    </div>
                  </div>
                </div>

                <div className="enhanced-summary-card operations-allocation">
                  <div className="card-header">
                    <div className="card-icon">⚙️</div>
                    <div className="card-badge">Operations</div>
                  </div>
                  <div className="card-content">
                    <h4>Operations Allocation</h4>
                    <div className="enhanced-summary-value">~70%</div>
                    <p>Dedicated to mining operations and expansion</p>
                    <div className="allocation-breakdown">
                      <div className="breakdown-item">
                        <span>Equipment & Infrastructure</span>
                        <span>45%</span>
                      </div>
                      <div className="breakdown-item">
                        <span>Operational Costs</span>
                        <span>25%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="enhanced-summary-card csr-allocation">
                  <div className="card-header">
                    <div className="card-icon">🌍</div>
                    <div className="card-badge">CSR Impact</div>
                  </div>
                  <div className="card-content">
                    <h4>CSR Allocation</h4>
                    <div className="enhanced-summary-value">~30%</div>
                    <p>Community impact and social responsibility</p>
                    <div className="allocation-breakdown">
                      <div className="breakdown-item">
                        <span>Community Development</span>
                        <span>20%</span>
                      </div>
                      <div className="breakdown-item">
                        <span>Environmental Programs</span>
                        <span>10%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="allocation-insights">
                <div className="insight-card">
                  <div className="insight-icon">📊</div>
                  <div className="insight-content">
                    <h4>Strategic Balance</h4>
                    <p>Our 70/30 allocation ensures sustainable operations while maintaining strong community impact and environmental responsibility.</p>
                  </div>
                </div>
                <div className="insight-card">
                  <div className="insight-icon">🚀</div>
                  <div className="insight-content">
                    <h4>Growth Potential</h4>
                    <p>Operational focus enables rapid expansion and increased production capacity across multiple mining sites.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Operational Metrics */}
      {activeTable === 'operations' && (
        <section className="data-section">
          <div className="container">
            <div className="section-header">
              <h2>Operational Parameters & Metrics</h2>
              <p>Technical specifications and operational assumptions used in calculations</p>
            </div>

            <div className="enhanced-metrics-grid">
              {operationalMetrics.map((metric, index) => (
                <div key={index} className="enhanced-metric-card">
                  <div className="metric-card-header">
                    <div className="metric-icon">
                      {index === 0 && '🏭'}
                      {index === 1 && '⚙️'}
                      {index === 2 && '📊'}
                      {index === 3 && '🎯'}
                      {index === 4 && '💰'}
                      {index === 5 && '⏱️'}
                      {index === 6 && '🔧'}
                      {index === 7 && '📈'}
                    </div>
                    <div className="metric-category">
                      {index < 5 ? 'Primary Metric' : 'Secondary Metric'}
                    </div>
                  </div>
                  <div className="metric-content">
                    <h3>{metric.metric}</h3>
                    <div className="enhanced-metric-value">{metric.value}</div>
                    <p className="metric-description">{metric.description}</p>
                  </div>
                  <div className="metric-footer">
                    <div className="metric-status">
                      <div className="status-indicator active"></div>
                      <span>Validated</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Enhanced Data Sources & Validation Section */}
            <div className="enhanced-operational-notes">
              <div className="notes-header">
                <div className="notes-icon">🔍</div>
                <div className="notes-content">
                  <h3>Data Sources & Validation</h3>
                  <p>Comprehensive verification and industry-standard benchmarking for all operational parameters</p>
                </div>
              </div>

              <div className="enhanced-notes-grid">
                <div className="enhanced-note-card geological">
                  <div className="note-card-header">
                    <div className="note-icon-wrapper">
                      <div className="note-icon">🔬</div>
                    </div>
                    <div className="note-badge">Geological</div>
                  </div>
                  <div className="note-content">
                    <h4>Geological Data</h4>
                    <p>Based on comprehensive 2020 geotechnical report confirming site viability and detailed gold content analysis across multiple drilling sites</p>
                    <div className="validation-details">
                      <div className="validation-item">
                        <span className="validation-label">Report Date:</span>
                        <span className="validation-value">2020</span>
                      </div>
                      <div className="validation-item">
                        <span className="validation-label">Confidence Level:</span>
                        <span className="validation-value">95%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="enhanced-note-card equipment">
                  <div className="note-card-header">
                    <div className="note-icon-wrapper">
                      <div className="note-icon">🏭</div>
                    </div>
                    <div className="note-badge">Equipment</div>
                  </div>
                  <div className="note-content">
                    <h4>Equipment Specifications</h4>
                    <p>Plant capacity calculations based on verified manufacturer specifications for jaw crushers, ball mills, and gravity separation systems</p>
                    <div className="validation-details">
                      <div className="validation-item">
                        <span className="validation-label">Manufacturers:</span>
                        <span className="validation-value">3 Verified</span>
                      </div>
                      <div className="validation-item">
                        <span className="validation-label">Capacity Rating:</span>
                        <span className="validation-value">Certified</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="enhanced-note-card benchmarks">
                  <div className="note-card-header">
                    <div className="note-icon-wrapper">
                      <div className="note-icon">📊</div>
                    </div>
                    <div className="note-badge">Benchmarks</div>
                  </div>
                  <div className="note-content">
                    <h4>Industry Benchmarks</h4>
                    <p>Operating costs and recovery rates benchmarked against similar alluvial gold mining operations across Africa and globally</p>
                    <div className="validation-details">
                      <div className="validation-item">
                        <span className="validation-label">Sample Size:</span>
                        <span className="validation-value">25 Operations</span>
                      </div>
                      <div className="validation-item">
                        <span className="validation-label">Data Currency:</span>
                        <span className="validation-value">2023-2024</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="enhanced-note-card market">
                  <div className="note-card-header">
                    <div className="note-icon-wrapper">
                      <div className="note-icon">💰</div>
                    </div>
                    <div className="note-badge">Market Data</div>
                  </div>
                  <div className="note-content">
                    <h4>Market Data</h4>
                    <p>Gold prices based on current London Bullion Market Association (LBMA) rates with real-time market tracking and analysis</p>
                    <div className="validation-details">
                      <div className="validation-item">
                        <span className="validation-label">Source:</span>
                        <span className="validation-value">LBMA</span>
                      </div>
                      <div className="validation-item">
                        <span className="validation-label">Update Frequency:</span>
                        <span className="validation-value">Real-time</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="validation-summary">
                <div className="summary-card">
                  <div className="summary-icon">✅</div>
                  <div className="summary-content">
                    <h4>Validation Complete</h4>
                    <p>All operational parameters have been independently verified and cross-referenced with industry standards</p>
                  </div>
                </div>
                <div className="summary-card">
                  <div className="summary-icon">🎯</div>
                  <div className="summary-content">
                    <h4>Conservative Estimates</h4>
                    <p>All projections use conservative estimates to ensure realistic expectations and sustainable operations</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Enhanced Risk Factors Section */}
      <section className="enhanced-risk-factors-section">
        <div className="container">
          <div className="section-header">
            <div className="header-icon">⚠️</div>
            <div className="header-content">
              <h2>Risk Factors & Disclaimers</h2>
              <p>Comprehensive risk assessment and important considerations for potential shareholders</p>
            </div>
          </div>

          <div className="enhanced-risk-grid">
            <div className="enhanced-risk-card market-risk">
              <div className="risk-card-header">
                <div className="risk-icon-wrapper">
                  <div className="risk-icon">📈</div>
                </div>
                <div className="risk-severity high">High Impact</div>
              </div>
              <div className="risk-content">
                <h3>Market Risk</h3>
                <p>Gold prices fluctuate based on global economic conditions, currency movements, and market sentiment. Price volatility can significantly impact dividend distributions.</p>
                <div className="risk-mitigation">
                  <strong>Mitigation:</strong> Conservative pricing models and diversified operational approach
                </div>
              </div>
            </div>

            <div className="enhanced-risk-card operational-risk">
              <div className="risk-card-header">
                <div className="risk-icon-wrapper">
                  <div className="risk-icon">🏭</div>
                </div>
                <div className="risk-severity medium">Medium Impact</div>
              </div>
              <div className="risk-content">
                <h3>Operational Risk</h3>
                <p>Mining operations may face equipment failures, weather delays, or regulatory changes that could affect production schedules and costs.</p>
                <div className="risk-mitigation">
                  <strong>Mitigation:</strong> Preventive maintenance programs and operational contingency planning
                </div>
              </div>
            </div>

            <div className="enhanced-risk-card political-risk">
              <div className="risk-card-header">
                <div className="risk-icon-wrapper">
                  <div className="risk-icon">🌍</div>
                </div>
                <div className="risk-severity medium">Medium Impact</div>
              </div>
              <div className="risk-content">
                <h3>Political Risk</h3>
                <p>Operations in multiple African countries are subject to political stability, regulatory changes, and government policy shifts.</p>
                <div className="risk-mitigation">
                  <strong>Mitigation:</strong> Strong local partnerships and compliance with all regulatory requirements
                </div>
              </div>
            </div>

            <div className="enhanced-risk-card performance-risk">
              <div className="risk-card-header">
                <div className="risk-icon-wrapper">
                  <div className="risk-icon">📊</div>
                </div>
                <div className="risk-severity low">Low Impact</div>
              </div>
              <div className="risk-content">
                <h3>Performance Risk</h3>
                <p>Actual recovery rates and operational efficiency may differ from projections, affecting overall profitability and dividend potential.</p>
                <div className="risk-mitigation">
                  <strong>Mitigation:</strong> Conservative estimates and continuous process optimization
                </div>
              </div>
            </div>
          </div>

          <div className="enhanced-disclaimer">
            <div className="disclaimer-header">
              <div className="disclaimer-icon">📋</div>
              <h3>Important Shareholder Disclaimer</h3>
            </div>
            <div className="disclaimer-content">
              <p>
                <strong>Investment Disclosure:</strong> All financial projections and dividend estimates are based on current operational
                parameters, prevailing market conditions, and conservative industry benchmarks. Historical performance and projected
                returns do not guarantee future dividend payments or share value appreciation.
              </p>
              <p>
                <strong>Due Diligence Required:</strong> Potential shareholders are strongly advised to conduct independent due diligence,
                review all available documentation, and carefully consider their risk tolerance and financial objectives before making
                any share purchase decisions.
              </p>
              <p>
                <strong>Regulatory Compliance:</strong> This information is provided for educational purposes and does not constitute
                financial advice. Aureus Alliance Holdings operates in full compliance with applicable securities regulations.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FinancialDataPage;
