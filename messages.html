:8000/:90 The Content Security Policy directive 'frame-ancestors' is ignored when delivered via a <meta> element.
(index):594 🚀 Loading universal error fixes...
(index):665 🚨 Global error caught: Illegal invocation
client:789 [vite] connecting...
errorMonitoring.js:45 🛡️ Initializing comprehensive error monitoring...
errorMonitoring.js:66 ✅ Error monitoring system initialized
index.html:96 ✅ Error monitoring system loaded
client:912 [vite] connected.
supabase.ts:5 🔍 Raw Environment Variables:
supabase.ts:6 import.meta.env.VITE_SUPABASE_URL: https://fgubaqoftdeefcakejwu.supabase.co
supabase.ts:7 import.meta.env.VITE_SUPABASE_ANON_KEY exists: true
supabase.ts:8 All VITE_ vars: (7) ['VITE_API_BASE_URL', 'VITE_RESEND_API_KEY', 'VITE_RESEND_FROM_EMAIL', 'VITE_RESEND_FROM_NAME', 'VITE_SUPABASE_ANON_KEY', 'VITE_SUPABASE_SERVICE_ROLE_KEY', 'VITE_SUPABASE_URL']
supabase.ts:15 🔍 Supabase Config Check (FORCED):
supabase.ts:16 URL: https://fgubaqoftdeefcakejwu.supabase.co
supabase.ts:17 Anon Key exists: true
supabase.ts:18 Anon Key length: 208
supabase.ts:50 ✅ Supabase configuration looks good
supabase.ts:92 ✅ Supabase client created successfully
supabase.ts:131 🔧 Service role key available (FORCED): true
supabase.ts:132 🔧 Service role key length: 219
supabase.ts:133 🔧 Service role key starts with: eyJhbGciOiJIUzI1NiIs...
supabase.ts:135 🔧 Creating service role client with URL (FORCED): https://fgubaqoftdeefcakejwu.supabase.co
supabase.ts:136 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
overrideMethod @ hook.js:608
_GoTrueClient @ @supabase_supabase-js.js?v=0292d41e:5157
SupabaseAuthClient @ @supabase_supabase-js.js?v=0292d41e:7235
_initSupabaseAuthClient @ @supabase_supabase-js.js?v=0292d41e:7433
SupabaseClient @ @supabase_supabase-js.js?v=0292d41e:7306
createClient @ @supabase_supabase-js.js?v=0292d41e:7473
getServiceRoleClient @ supabase.ts:136
CompetitionService @ competitionService.ts:72
(anonymous) @ competitionService.ts:675
supabase.ts:148 🔧 Service role client created successfully
supabase.ts:151 🔧 Testing service role client...
supportTicketNotificationService.ts:31 ✅ Support ticket notification service initialized
resendEmailService.ts:27 ✅ Resend email service initialized
kycNotificationService.ts:28 ✅ KYC Notification Service initialized with existing email service
constants.ts:16 🔧 API Configuration (FORCED VERCEL DETECTION): {hostname: 'localhost', isVercelProduction: false, isViteProd: false, isProduction: false, VITE_API_BASE_URL: 'http://localhost:8002', …}
realtimeValidation.ts:14 🚨 EMERGENCY API URL FIX - BUILD TIMESTAMP: 2025-01-15-14:30: {hostname: 'localhost', originalAPI_BASE_URL: 'http://localhost:8002', FORCE_API_BASE_URL: 'http://localhost:8002', isVercel: false, buildTime: '2025-01-15-14:30'}
App.tsx:1779 🔄 URL routing useEffect triggered {path: '/', currentSection: 'home', isMobile: false}
referralPersistence.ts:171 ℹ️ No referral information found in URL
referralPersistence.ts:229 ℹ️ No active referral session
useGoldPrice.ts:54 💰 Using simulated gold price: $108,256/kg
App.tsx:2343 🔍 Checking for existing session...
App.tsx:2344 🔍 [DEBUG] localStorage aureus_telegram_user: null
App.tsx:2345 🔍 [DEBUG] localStorage aureus_test_user: null
supabase.ts:1544 📧 Using email user from localStorage
emailProcessingService.ts:148 Email processing service started - checking every 30 seconds
App.tsx:2464 ✅ Email processing service initialized
useSiteContent.ts:22 🔄 Loading site content...
App.tsx:1779 🔄 URL routing useEffect triggered {path: '/', currentSection: 'home', isMobile: false}
referralPersistence.ts:171 ℹ️ No referral information found in URL
referralPersistence.ts:229 ℹ️ No active referral session
useGoldPrice.ts:54 💰 Using simulated gold price: $108,256/kg
App.tsx:2343 🔍 Checking for existing session...
App.tsx:2344 🔍 [DEBUG] localStorage aureus_telegram_user: null
App.tsx:2345 🔍 [DEBUG] localStorage aureus_test_user: null
supabase.ts:1544 📧 Using email user from localStorage
emailProcessingService.ts:23 Email processing already in progress, skipping...
emailProcessingService.ts:148 Email processing service started - checking every 30 seconds
App.tsx:2464 ✅ Email processing service initialized
useSiteContent.ts:22 🔄 Loading site content...
jquery-3.4.1.min.js:2 Error: <path> attribute d: Expected number, "…               tc0.2,0,0.4-0.2,0…".
xe @ jquery-3.4.1.min.js:2
He @ jquery-3.4.1.min.js:2
append @ jquery-3.4.1.min.js:2
(anonymous) @ translateContent.js:1
e @ jquery-3.4.1.min.js:2
t @ jquery-3.4.1.min.js:2
setTimeout
(anonymous) @ jquery-3.4.1.min.js:2
c @ jquery-3.4.1.min.js:2
fireWith @ jquery-3.4.1.min.js:2
fire @ jquery-3.4.1.min.js:2
c @ jquery-3.4.1.min.js:2
fireWith @ jquery-3.4.1.min.js:2
ready @ jquery-3.4.1.min.js:2
setTimeout
(anonymous) @ jquery-3.4.1.min.js:2
(anonymous) @ jquery-3.4.1.min.js:2
(anonymous) @ jquery-3.4.1.min.js:2
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
XHR finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
Fetch finished loading: GET "<URL>".
supabase.ts:1605 ✅ Profile picture URL refreshed from database: null
supabase.ts:1452 🔍 getUserType: Analyzing user: {id: 4, email: '<EMAIL>', role: 'super_admin', telegram_id: **********, account_type: 'email'}
 ✅ getUserType: User is affiliate (Telegram user)
 ✅ Email user loaded from localStorage: {id: '635497f3-4d46-47f7-984b-60e0abc4c614', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
 🔍 [DEBUG] getCurrentUser result: {id: '635497f3-4d46-47f7-984b-60e0abc4c614', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
App.tsx:2376 ✅ Found existing session: {id: '635497f3-4d46-47f7-984b-60e0abc4c614', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
App.tsx:2377 🔍 [DEBUG] currentUser.needsProfileCompletion: false
App.tsx:2378 🔍 [DEBUG] currentUser.user_metadata?.profile_completion_required: false
App.tsx:2379 🔍 [DEBUG] currentUser.database_user?.telegram_id: **********
App.tsx:2385 🔍 Current path during session check: /
App.tsx:2429 ✅ Going to dashboard - profile completion handled in legal documents
App.tsx:2958 🔍 Dashboard route guard check: {needsProfileCompletion: false, profile_completion_required: false, currentSection: 'dashboard', userEmail: '<EMAIL>', hasDatabase: true, …}
App.tsx:2974 ✅ Dashboard access granted - user authenticated
App.tsx:2958 🔍 Dashboard route guard check: {needsProfileCompletion: false, profile_completion_required: false, currentSection: 'dashboard', userEmail: '<EMAIL>', hasDatabase: true, …}
App.tsx:2974 ✅ Dashboard access granted - user authenticated
supabase.ts:157 🔧 Service role client test successful, user count: 257
(index):673 Fetch failed loading: HEAD "https://fgubaqoftdeefcakejwu.supabase.co/rest/v1/users?select=count".
window.fetch @ (index):673
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3929
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3950
fulfilled @ @supabase_supabase-js.js?v=0292d41e:3902
Promise.then
step @ @supabase_supabase-js.js?v=0292d41e:3915
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3917
__awaiter6 @ @supabase_supabase-js.js?v=0292d41e:3899
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3940
then @ @supabase_supabase-js.js?v=0292d41e:89
getServiceRoleClient @ supabase.ts:153
CompetitionService @ competitionService.ts:72
(anonymous) @ competitionService.ts:675
supabase.ts:1605 ✅ Profile picture URL refreshed from database: null
supabase.ts:1452 🔍 getUserType: Analyzing user: {id: 4, email: '<EMAIL>', role: 'super_admin', telegram_id: **********, account_type: 'email'}
supabase.ts:1489 ✅ getUserType: User is affiliate (Telegram user)
supabase.ts:1619 ✅ Email user loaded from localStorage: {id: '635497f3-4d46-47f7-984b-60e0abc4c614', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
App.tsx:2373 🔍 [DEBUG] getCurrentUser result: {id: '635497f3-4d46-47f7-984b-60e0abc4c614', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
App.tsx:2376 ✅ Found existing session: {id: '635497f3-4d46-47f7-984b-60e0abc4c614', email: '<EMAIL>', database_user: {…}, account_type: 'email', needsProfileCompletion: false, …}
App.tsx:2377 🔍 [DEBUG] currentUser.needsProfileCompletion: false
App.tsx:2378 🔍 [DEBUG] currentUser.user_metadata?.profile_completion_required: false
App.tsx:2379 🔍 [DEBUG] currentUser.database_user?.telegram_id: **********
App.tsx:2385 🔍 Current path during session check: /
App.tsx:2429 ✅ Going to dashboard - profile completion handled in legal documents
useSiteContent.ts:24 📄 Site content loaded: (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
useSiteContent.ts:24 📄 Site content loaded: (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
App.tsx:2958 🔍 Dashboard route guard check: {needsProfileCompletion: false, profile_completion_required: false, currentSection: 'dashboard', userEmail: '<EMAIL>', hasDatabase: true, …}
App.tsx:2974 ✅ Dashboard access granted - user authenticated
App.tsx:2958 🔍 Dashboard route guard check: {needsProfileCompletion: false, profile_completion_required: false, currentSection: 'dashboard', userEmail: '<EMAIL>', hasDatabase: true, …}
App.tsx:2974 ✅ Dashboard access granted - user authenticated
supabase.ts:23 🔍 Testing database connection...
emailProcessingService.ts:46 No pending email notifications to process
jquery-3.4.1.min.js:2 XHR finished loading: POST "https://backenster.com/api/app/config".
send @ jquery-3.4.1.min.js:2
ajax @ jquery-3.4.1.min.js:2
readBackensterParams @ translate.js:1
(anonymous) @ translate.js:1
(anonymous) @ piwik.js:1
supabase.ts:33 ✅ Database connection test successful, found 1 users
svgCertificateGenerator.ts:88 ✅ SVG template loaded successfully from Supabase storage
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
(index):673 Fetch finished loading: POST "https://fgubaqoftdeefcakejwu.supabase.co/rest/v1/email_campaigns?select=*".
window.fetch @ (index):673
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3929
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3950
fulfilled @ @supabase_supabase-js.js?v=0292d41e:3902
Promise.then
step @ @supabase_supabase-js.js?v=0292d41e:3915
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3917
__awaiter6 @ @supabase_supabase-js.js?v=0292d41e:3899
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3940
then @ @supabase_supabase-js.js?v=0292d41e:89
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
(index):673  POST http://localhost:8000/api/send-marketing-email 500 (Internal Server Error)
window.fetch @ (index):673
handleSendEmail @ MarketingToolsManager.tsx:589
await in handleSendEmail
executeDispatch @ react-dom_client.js?v=0292d41e:11736
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
processDispatchQueue @ react-dom_client.js?v=0292d41e:11772
(anonymous) @ react-dom_client.js?v=0292d41e:12182
batchedUpdates$1 @ react-dom_client.js?v=0292d41e:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=0292d41e:11877
dispatchEvent @ react-dom_client.js?v=0292d41e:14792
dispatchDiscreteEvent @ react-dom_client.js?v=0292d41e:14773
<form>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
MarketingToolsManager @ MarketingToolsManager.tsx:2179
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performSyncWorkOnRoot @ react-dom_client.js?v=0292d41e:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=0292d41e:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=0292d41e:11558
(anonymous) @ react-dom_client.js?v=0292d41e:11649
<MarketingToolsManager>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
renderSectionContent @ AffiliateDashboard.tsx:470
AffiliateDashboard @ AffiliateDashboard.tsx:592
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performSyncWorkOnRoot @ react-dom_client.js?v=0292d41e:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=0292d41e:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=0292d41e:11558
(anonymous) @ react-dom_client.js?v=0292d41e:11649
<AffiliateDashboard>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
App @ App.tsx:2979
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=0292d41e:11623
performWorkUntilDeadline @ react-dom_client.js?v=0292d41e:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
(anonymous) @ index.tsx:21
(index):28 ❌ Error sending <NAME_EMAIL>: SyntaxError: Failed to execute 'json' on 'Response': Unexpected end of JSON input
    at handleSendEmail (MarketingToolsManager.tsx:605:46)
console.error @ (index):28
console.error @ (index):161
overrideMethod @ hook.js:608
console.error @ (index):711
console.error @ errorMonitoring.js:124
handleSendEmail @ MarketingToolsManager.tsx:633
await in handleSendEmail
executeDispatch @ react-dom_client.js?v=0292d41e:11736
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
processDispatchQueue @ react-dom_client.js?v=0292d41e:11772
(anonymous) @ react-dom_client.js?v=0292d41e:12182
batchedUpdates$1 @ react-dom_client.js?v=0292d41e:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=0292d41e:11877
dispatchEvent @ react-dom_client.js?v=0292d41e:14792
dispatchDiscreteEvent @ react-dom_client.js?v=0292d41e:14773
<form>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
MarketingToolsManager @ MarketingToolsManager.tsx:2179
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performSyncWorkOnRoot @ react-dom_client.js?v=0292d41e:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=0292d41e:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=0292d41e:11558
(anonymous) @ react-dom_client.js?v=0292d41e:11649
<MarketingToolsManager>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
renderSectionContent @ AffiliateDashboard.tsx:470
AffiliateDashboard @ AffiliateDashboard.tsx:592
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performSyncWorkOnRoot @ react-dom_client.js?v=0292d41e:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=0292d41e:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=0292d41e:11558
(anonymous) @ react-dom_client.js?v=0292d41e:11649
<AffiliateDashboard>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
App @ App.tsx:2979
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=0292d41e:11623
performWorkUntilDeadline @ react-dom_client.js?v=0292d41e:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
(anonymous) @ index.tsx:21
(index):673 Fetch failed loading: POST "http://localhost:8000/api/send-marketing-email".
window.fetch @ (index):673
handleSendEmail @ MarketingToolsManager.tsx:589
await in handleSendEmail
executeDispatch @ react-dom_client.js?v=0292d41e:11736
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
processDispatchQueue @ react-dom_client.js?v=0292d41e:11772
(anonymous) @ react-dom_client.js?v=0292d41e:12182
batchedUpdates$1 @ react-dom_client.js?v=0292d41e:2628
dispatchEventForPluginEventSystem @ react-dom_client.js?v=0292d41e:11877
dispatchEvent @ react-dom_client.js?v=0292d41e:14792
dispatchDiscreteEvent @ react-dom_client.js?v=0292d41e:14773
<form>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
MarketingToolsManager @ MarketingToolsManager.tsx:2179
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performSyncWorkOnRoot @ react-dom_client.js?v=0292d41e:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=0292d41e:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=0292d41e:11558
(anonymous) @ react-dom_client.js?v=0292d41e:11649
<MarketingToolsManager>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
renderSectionContent @ AffiliateDashboard.tsx:470
AffiliateDashboard @ AffiliateDashboard.tsx:592
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performSyncWorkOnRoot @ react-dom_client.js?v=0292d41e:11635
flushSyncWorkAcrossRoots_impl @ react-dom_client.js?v=0292d41e:11536
processRootScheduleInMicrotask @ react-dom_client.js?v=0292d41e:11558
(anonymous) @ react-dom_client.js?v=0292d41e:11649
<AffiliateDashboard>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
App @ App.tsx:2979
react-stack-bottom-frame @ react-dom_client.js?v=0292d41e:17424
renderWithHooksAgain @ react-dom_client.js?v=0292d41e:4281
renderWithHooks @ react-dom_client.js?v=0292d41e:4217
updateFunctionComponent @ react-dom_client.js?v=0292d41e:6619
beginWork @ react-dom_client.js?v=0292d41e:7654
runWithFiberInDEV @ react-dom_client.js?v=0292d41e:1485
performUnitOfWork @ react-dom_client.js?v=0292d41e:10868
workLoopSync @ react-dom_client.js?v=0292d41e:10728
renderRootSync @ react-dom_client.js?v=0292d41e:10711
performWorkOnRoot @ react-dom_client.js?v=0292d41e:10330
performWorkOnRootViaSchedulerTask @ react-dom_client.js?v=0292d41e:11623
performWorkUntilDeadline @ react-dom_client.js?v=0292d41e:36
<App>
exports.jsxDEV @ react_jsx-dev-runtime.js?v=0292d41e:250
(anonymous) @ index.tsx:21
(index):673 Fetch finished loading: POST "https://fgubaqoftdeefcakejwu.supabase.co/rest/v1/email_sends?columns=%22campaign_id%22%2C%22lead_id%22%2C%22email_address%22%2C%22status%22%2C%22sent_at%22%2C%22error_message%22".
window.fetch @ (index):673
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3929
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3950
fulfilled @ @supabase_supabase-js.js?v=0292d41e:3902
Promise.then
step @ @supabase_supabase-js.js?v=0292d41e:3915
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3917
__awaiter6 @ @supabase_supabase-js.js?v=0292d41e:3899
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3940
then @ @supabase_supabase-js.js?v=0292d41e:89
(index):673 Fetch finished loading: PATCH "https://fgubaqoftdeefcakejwu.supabase.co/rest/v1/email_campaigns?id=eq.d9e3075e-f211-4bda-8529-07721965c331".
window.fetch @ (index):673
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3929
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3950
fulfilled @ @supabase_supabase-js.js?v=0292d41e:3902
Promise.then
step @ @supabase_supabase-js.js?v=0292d41e:3915
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3917
__awaiter6 @ @supabase_supabase-js.js?v=0292d41e:3899
(anonymous) @ @supabase_supabase-js.js?v=0292d41e:3940
then @ @supabase_supabase-js.js?v=0292d41e:89
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
MarketingToolsManager.tsx:113 MarketingToolsManager - affiliateData: {user: {…}, referralCount: 122, commissionBalance: {…}}
