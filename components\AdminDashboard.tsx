import React, { useState, useEffect } from 'react'
import { signOut, getCurrentUser, getSiteContent } from '../lib/supabase'
import { checkAdminStatus } from '../lib/adminAuth'
import { useAdminNotifications, getBadgeCountForTab } from '../hooks/useAdminNotifications'
// import { ContentEditor } from './ContentEditor'
// import { MiningDataEditor } from './MiningDataEditor'
import { GalleryManager } from './admin/GalleryManager'
import { DatabaseTest } from './admin/DatabaseTest'
import { DatabaseDebugger } from './admin/DatabaseDebugger'
import { SponsorManager } from './admin/SponsorManager'
import { PaymentManager } from './admin/PaymentManager'
import { UserManager } from './admin/UserManager'
import { AuditLogViewer } from './admin/AuditLogViewer'
import { MarketingMaterialsManager } from './admin/MarketingMaterialsManager'
import CertificateManagement from './admin/CertificateManagement'
import CertificateTestSystem from './admin/CertificateTestSystem'
import { AdminWalletManager } from './admin/AdminWalletManager'
import { TestUserAuditReport } from './admin/TestUserAuditReport'
import { FundManagementDashboard } from './admin/FundManagementDashboard'
import { DownlineManager } from './admin/DownlineManager'
import { PhaseCommissionTest } from './test/PhaseCommissionTest'
import PhaseCompetitionManager from './admin/PhaseCompetitionManager'
import SupportInbox from './admin/SupportInbox'
import ComprehensiveSupportDashboard from './admin/ComprehensiveSupportDashboard'
import CommissionWithdrawalManager from './admin/CommissionWithdrawalManager'
import { CommissionConversionManager } from './admin/CommissionConversionManager'
import { TrainingAcademyManager } from './admin/TrainingAcademyManager'
import MeetingSchedulerManager from './admin/MeetingSchedulerManager'
import ListManagerDashboard from './admin/ListManagerDashboard'
import ShareLedgerReportSystem from './admin/ShareLedgerReportSystem'
import { TransactionManagementSystem } from './admin/TransactionManagementSystem'
import '../styles/TransactionManagementSystem.css'
import KYCManagementDashboard from './admin/KYCManagementDashboard'
import CommissionAuditDashboard from './admin/CommissionAuditDashboard'
import FinancialAuditReportGenerator from './admin/FinancialAuditReportGenerator'

interface AdminDashboardProps {
  onLogout: () => void
  user?: any
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({ onLogout, user: propUser }) => {
  const [activeTab, setActiveTab] = useState('users')
  const [user, setUser] = useState<any>(propUser || null)
  const [loading, setLoading] = useState(true)
  const [adminVerified, setAdminVerified] = useState(false)
  const { counts: notificationCounts, loading: notificationsLoading } = useAdminNotifications()

  useEffect(() => {
    loadUser()
  }, [])

  const loadUser = async () => {
    try {
      const currentUser = await getCurrentUser()

      // 🔒 CRITICAL SECURITY CHECK: Verify admin status
      if (currentUser?.email) {
        console.log('🔍 AdminDashboard: Verifying admin status for:', currentUser.email)
        const adminUser = await checkAdminStatus(currentUser.email)

        if (!adminUser) {
          console.log('❌ AdminDashboard: Access denied - user is not an admin')
          alert('Access denied. You do not have admin privileges.')
          onLogout()
          return
        }

        console.log('✅ AdminDashboard: Admin verified:', adminUser.email, adminUser.role)
        setUser({ ...currentUser, adminUser })
        setAdminVerified(true)
      } else {
        console.log('❌ AdminDashboard: No user found')
        onLogout()
        return
      }
    } catch (error) {
      console.error('Error loading user:', error)
      onLogout()
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await signOut()
    onLogout()
  }

  const tabs = [
    { id: 'users', name: 'User Management', icon: '👥' },
    { id: 'kyc', name: 'KYC Management', icon: '🔍' },
    { id: 'payments', name: 'Payment Management', icon: '💰' },
    { id: 'commissions', name: 'Commission & Withdrawals', icon: '💸' },
    { id: 'commission-conversions', name: 'Commission Conversions', icon: '🔄' },
    { id: 'commission-audit', name: 'Commission Audit', icon: '🔍' },
    { id: 'financial-audit', name: 'Financial Audit Reports', icon: '📊' },
    { id: 'reports', name: 'Share Ledger Reports', icon: '📊' },
    { id: 'transaction-management', name: 'Transaction Management', icon: '🔄' },
    { id: 'funds', name: 'Fund Management', icon: '💵' },
    { id: 'wallets', name: 'Wallet Management', icon: '🏦' },
    { id: 'certificates', name: 'Certificate Management', icon: '📜' },
    { id: 'cert-test', name: 'Certificate Test System', icon: '🧪' },
    { id: 'sponsors', name: 'Sponsor Management', icon: '🤝' },
    { id: 'downline', name: 'Downline Management', icon: '🌳' },
    { id: 'competitions', name: 'Competition Management', icon: '🏆' },
    { id: 'meetings', name: 'Meeting Scheduler', icon: '📅' },
    { id: 'gallery', name: 'Gallery Management', icon: '📸' },
    { id: 'marketing', name: 'Marketing Materials', icon: '📁' },
    { id: 'list-manager', name: 'List Manager', icon: '📧' },
    { id: 'audit', name: 'Audit Logs', icon: '📋' },
    { id: 'support', name: 'Support Dashboard', icon: '🆘' },
    { id: 'training', name: 'Training Academy', icon: '🎓' },
    { id: 'test-audit', name: 'Test User Audit', icon: '🔍' },
    { id: 'phase-test', name: 'Phase Commission Test', icon: '🧪' },
    { id: 'debug', name: 'Debug System', icon: '🐛' }
  ]

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#050505',
        color: '#FFFFFF'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '2px solid transparent',
            borderTop: '2px solid #FFD700',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#9CA3AF' }}>Verifying admin credentials...</p>
        </div>
      </div>
    )
  }

  // 🔒 SECURITY: Only render dashboard if admin is verified
  if (!adminVerified) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: '#050505',
        color: '#FFFFFF'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ color: '#EF4444', fontSize: '64px', marginBottom: '16px' }}>🚫</div>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: '#FFFFFF',
            marginBottom: '8px'
          }}>
            Access Denied
          </h2>
          <p style={{ color: '#9CA3AF', marginBottom: '16px' }}>
            You do not have admin privileges.
          </p>
          <button
            onClick={onLogout}
            style={{
              padding: '8px 16px',
              backgroundColor: '#DC2626',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#B91C1C'
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#DC2626'
            }}
          >
            Return to Login
          </button>
        </div>
      </div>
    )
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: '#050505',
      color: '#FFFFFF',
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: '#121212',
        borderBottom: '1px solid #1E1E1E',
        backdropFilter: 'blur(16px)',
        boxShadow: '0px 0px 24px rgba(255, 215, 0, 0.15)'
      }}>
        <div style={{ maxWidth: '1280px', margin: '0 auto', padding: '0 24px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '16px 0' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  background: '#FFD700',
                  borderRadius: '12px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 0 18px #FFD70066'
                }}>
                  <span style={{ color: '#000000', fontWeight: 'bold', fontSize: '18px' }}>A</span>
                </div>
                <h1 style={{
                  fontSize: '24px',
                  fontWeight: 'bold',
                  background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  margin: 0
                }}>
                  AUREUS Admin
                </h1>
              </div>
              <span style={{ color: '#4B5563' }}>|</span>
              <span style={{ color: '#9CA3AF' }}>Content Management System</span>
            </div>

            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <span style={{ color: '#9CA3AF' }}>Welcome, {user?.email}</span>

              {/* Debug Status Indicator */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '6px 12px',
                backgroundColor: 'rgba(31, 41, 55, 0.3)',
                border: '1px solid rgba(107, 114, 128, 0.3)',
                borderRadius: '8px'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  backgroundColor: '#10B981',
                  borderRadius: '50%'
                }}></div>
                <span style={{ color: '#9CA3AF', fontSize: '14px', fontWeight: '500' }}>Debug System</span>
                <span style={{ color: '#D1D5DB', fontSize: '12px' }}>Available</span>
              </div>

              <button
                onClick={handleLogout}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#DC2626',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = '#B91C1C'
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = '#DC2626'
                }}
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      <div style={{ maxWidth: '1280px', margin: '0 auto', padding: '0 24px 32px' }}>
        <div style={{ display: 'flex', gap: '32px' }}>
          {/* Sidebar Navigation */}
          <div style={{ width: '320px', flexShrink: 0 }}>
            <nav style={{
              backgroundColor: '#121212',
              borderRadius: '12px',
              padding: '16px',
              border: '1px solid #1E1E1E',
              backdropFilter: 'blur(16px)',
              boxShadow: '0px 0px 24px rgba(255, 215, 0, 0.15)'
            }}>
              <h2 style={{
                fontSize: '18px',
                fontWeight: '600',
                color: '#FFFFFF',
                marginBottom: '16px'
              }}>
                Sections
              </h2>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {tabs.map((tab) => {
                  const isActive = activeTab === tab.id
                  const badgeCount = getBadgeCountForTab(tab.id, notificationCounts)
                  return (
                    <button
                      key={tab.id}
                      onClick={() => {
                        console.log('Switching to tab:', tab.id);
                        setActiveTab(tab.id);
                      }}
                      style={{
                        width: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px',
                        padding: '12px',
                        borderRadius: '8px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        backgroundColor: isActive ? 'rgba(255, 215, 0, 0.1)' : 'transparent',
                        border: isActive ? '1px solid rgba(255, 215, 0, 0.3)' : '1px solid transparent',
                        color: isActive ? '#FFD700' : '#B0B0B0'
                      }}
                      onMouseEnter={(e) => {
                        if (!isActive) {
                          e.target.style.backgroundColor = 'rgba(30, 30, 30, 0.5)'
                          e.target.style.color = '#FFFFFF'
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (!isActive) {
                          e.target.style.backgroundColor = 'transparent'
                          e.target.style.color = '#B0B0B0'
                        }
                      }}
                    >
                      <span style={{ fontSize: '20px' }}>{tab.icon}</span>
                      <span style={{ fontWeight: '500', flex: 1 }}>{tab.name}</span>
                      {badgeCount > 0 && (
                        <span style={{
                          backgroundColor: '#EF4444',
                          color: 'white',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          borderRadius: '10px',
                          padding: '2px 6px',
                          minWidth: '18px',
                          textAlign: 'center',
                          lineHeight: '14px'
                        }}>
                          {badgeCount > 99 ? '99+' : badgeCount}
                        </span>
                      )}
                    </button>
                  )
                })}
              </div>
            </nav>
          </div>

          {/* Main Content Area */}
          <div style={{ flex: 1 }}>
            <div style={{
              backgroundColor: '#121212',
              borderRadius: '12px',
              padding: '24px',
              border: '1px solid #1E1E1E',
              backdropFilter: 'blur(16px)',
              boxShadow: '0px 0px 24px rgba(255, 215, 0, 0.15)'
            }}>
              <div style={{ marginBottom: '16px', fontSize: '12px', color: '#6B7280' }}>
                DEBUG: activeTab = "{activeTab}"
              </div>
              {activeTab === 'gallery' ? (
                <GalleryManager />
              ) : activeTab === 'sponsors' ? (
                <SponsorManager currentUser={user} />
              ) : activeTab === 'downline' ? (
                <DownlineManager />
              ) : activeTab === 'competitions' ? (
                <PhaseCompetitionManager />
              ) : activeTab === 'meetings' ? (
                <MeetingSchedulerManager />
              ) : activeTab === 'payments' ? (
                <PaymentManager currentUser={user} />
              ) : activeTab === 'commissions' ? (
                <CommissionWithdrawalManager currentUser={user} />
              ) : activeTab === 'commission-conversions' ? (
                <CommissionConversionManager currentUser={user} />
              ) : activeTab === 'commission-audit' ? (
                <CommissionAuditDashboard />
              ) : activeTab === 'financial-audit' ? (
                <FinancialAuditReportGenerator currentUser={user} />
              ) : activeTab === 'reports' ? (
                <ShareLedgerReportSystem currentUser={user} />
              ) : activeTab === 'transaction-management' ? (
                <TransactionManagementSystem currentUser={user} />
              ) : activeTab === 'funds' ? (
                <FundManagementDashboard />
              ) : activeTab === 'wallets' ? (
                <AdminWalletManager />
              ) : activeTab === 'certificates' ? (
                <CertificateManagement />
              ) : activeTab === 'cert-test' ? (
                <CertificateTestSystem />
              ) : activeTab === 'users' ? (
                <UserManager currentUser={user} />
              ) : activeTab === 'kyc' ? (
                <KYCManagementDashboard currentUser={user} />
              ) : activeTab === 'marketing' ? (
                <MarketingMaterialsManager currentUser={user} />
              ) : activeTab === 'list-manager' ? (
                <ListManagerDashboard currentUser={user} />
              ) : activeTab === 'audit' ? (
                <AuditLogViewer />
              ) : activeTab === 'support' ? (
                <ComprehensiveSupportDashboard currentUser={user?.adminUser} />
              ) : activeTab === 'training' ? (
                <TrainingAcademyManager currentUser={user} />
              ) : activeTab === 'test-audit' ? (
                <TestUserAuditReport />
              ) : activeTab === 'phase-test' ? (
                <PhaseCommissionTest />
              ) : activeTab === 'debug' ? (
                <div>
                  <h3 style={{
                    fontSize: '20px',
                    color: '#F59E0B',
                    marginBottom: '24px',
                    fontWeight: '600'
                  }}>
                    🐛 Debug Panel
                  </h3>
                  <p style={{ color: '#9CA3AF', marginBottom: '16px' }}>
                    Current active tab: {activeTab}
                  </p>
                  <div style={{
                    backgroundColor: 'rgba(31, 41, 55, 0.5)',
                    borderRadius: '8px',
                    border: '1px solid #374151',
                    padding: '24px'
                  }}>
                    <h4 style={{
                      color: '#FFFFFF',
                      fontSize: '18px',
                      marginBottom: '16px',
                      fontWeight: '600'
                    }}>
                      Debug Panel is Working!
                    </h4>
                    <p style={{ color: '#9CA3AF', marginBottom: '16px' }}>
                      If you can see this, the debug tab is functioning correctly.
                    </p>
                    <div style={{
                      backgroundColor: 'rgba(59, 130, 246, 0.1)',
                      border: '1px solid rgba(59, 130, 246, 0.3)',
                      borderRadius: '6px',
                      padding: '16px'
                    }}>
                      <p style={{ color: '#60A5FA', fontSize: '14px' }}>
                        <strong>Next:</strong> Database connection test will be loaded here.
                      </p>
                    </div>
                  </div>
                  <div style={{ marginTop: '24px' }}>
                    <DatabaseTest />
                  </div>
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '32px 0' }}>
                  <h3 style={{
                    fontSize: '20px',
                    color: '#F59E0B',
                    marginBottom: '16px',
                    fontWeight: '600'
                  }}>
                    📝 Unknown Section
                  </h3>
                  <p style={{ color: '#9CA3AF' }}>
                    Section not found: <span style={{ color: '#FCD34D' }}>{activeTab}</span>
                  </p>
                  <p style={{ color: '#9CA3AF', marginTop: '8px' }}>
                    Please select a valid section from the sidebar.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Debug System Placeholder - Will be implemented later
const DebugSystemPlaceholder: React.FC = () => {
  return (
    <div style={{ textAlign: 'center', padding: '48px 0' }}>
      <div style={{ color: '#9CA3AF', fontSize: '18px', marginBottom: '16px' }}>🐛</div>
      <h2 style={{
        fontSize: '20px',
        fontWeight: '600',
        color: '#FFFFFF',
        marginBottom: '8px'
      }}>
        Debug System
      </h2>
      <p style={{ color: '#9CA3AF' }}>Debug system will be available in a future update.</p>
      <p style={{ color: '#D1D5DB', marginTop: '16px', fontSize: '14px' }}>Features coming soon:</p>
      <ul style={{
        color: '#9CA3AF',
        fontSize: '14px',
        marginTop: '8px',
        listStyle: 'none',
        padding: 0
      }}>
        <li style={{ marginBottom: '4px' }}>• Error monitoring and reporting</li>
        <li style={{ marginBottom: '4px' }}>• Performance tracking</li>
        <li style={{ marginBottom: '4px' }}>• Real-time debugging tools</li>
      </ul>
    </div>
  );
};

