/**
 * AUREUS ALLIANCE HOLDINGS - UNIFIED DESIGN SYSTEM JAVASCRIPT
 * Professional Corporate Design System Interactive Features
 * Version: 1.0.0
 * 
 * This file provides JavaScript functionality for:
 * - Theme switching (dark/light mode)
 * - Interactive styling behaviors
 * - Accessibility enhancements
 * - Smooth animations and transitions
 * - Dynamic styling features
 */

(function() {
  'use strict';

  // ========================================
  // THEME MANAGEMENT SYSTEM
  // ========================================

  class AureusThemeManager {
    constructor() {
      this.currentTheme = 'dark'; // Fixed to dark theme only
      this.init();
    }

    init() {
      // Force dark theme - no theme switching
      this.currentTheme = 'dark';
      this.applyTheme(this.currentTheme);
      // Remove theme toggle and system listener setup
    }

    applyTheme(theme) {
      // Force dark theme only
      document.documentElement.setAttribute('data-theme', 'dark');
      this.currentTheme = 'dark';
      localStorage.setItem('aureus-theme', 'dark');

      // Dispatch custom event for theme change
      window.dispatchEvent(new CustomEvent('themeChanged', {
        detail: { theme: 'dark' }
      }));
    }
  }

  // ========================================
  // SMOOTH SCROLL ENHANCEMENT
  // ========================================

  class AureusSmoothScroll {
    constructor() {
      this.init();
    }

    init() {
      // Enhanced smooth scrolling for anchor links
      document.addEventListener('click', (e) => {
        const link = e.target.closest('a[href^="#"]');
        if (link) {
          e.preventDefault();
          const targetId = link.getAttribute('href').substring(1);
          const targetElement = document.getElementById(targetId);
          
          if (targetElement) {
            this.scrollToElement(targetElement);
          }
        }
      });
    }

    scrollToElement(element) {
      const headerHeight = document.querySelector('.header')?.offsetHeight || 0;
      const targetPosition = element.offsetTop - headerHeight - 20;
      
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
    }
  }

  // ========================================
  // ACCESSIBILITY ENHANCEMENTS
  // ========================================

  class AureusAccessibility {
    constructor() {
      this.init();
    }

    init() {
      this.setupKeyboardNavigation();
      this.setupFocusManagement();
      this.setupReducedMotion();
    }

    setupKeyboardNavigation() {
      // Enhanced keyboard navigation for custom components
      document.addEventListener('keydown', (e) => {
        // Escape key to close modals/dropdowns
        if (e.key === 'Escape') {
          const activeModal = document.querySelector('.modal.active');
          if (activeModal) {
            this.closeModal(activeModal);
          }
        }

        // Enter/Space for custom buttons
        if ((e.key === 'Enter' || e.key === ' ') && e.target.classList.contains('btn-custom')) {
          e.preventDefault();
          e.target.click();
        }
      });
    }

    setupFocusManagement() {
      // Improve focus visibility
      document.addEventListener('focusin', (e) => {
        e.target.classList.add('focused');
      });

      document.addEventListener('focusout', (e) => {
        e.target.classList.remove('focused');
      });
    }

    setupReducedMotion() {
      // Respect user's motion preferences
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
      
      if (prefersReducedMotion.matches) {
        document.documentElement.style.setProperty('--animation-duration', '0.01ms');
        document.documentElement.style.setProperty('--transition-duration', '0.01ms');
      }
    }

    closeModal(modal) {
      modal.classList.remove('active');
      // Return focus to trigger element if available
      const trigger = modal.getAttribute('data-trigger');
      if (trigger) {
        const triggerElement = document.querySelector(`[data-modal-trigger="${trigger}"]`);
        if (triggerElement) {
          triggerElement.focus();
        }
      }
    }
  }

  // ========================================
  // INTERACTIVE ANIMATIONS
  // ========================================

  class AureusAnimations {
    constructor() {
      this.init();
    }

    init() {
      this.setupScrollAnimations();
      this.setupHoverEffects();
      this.setupLoadingStates();
    }

    setupScrollAnimations() {
      // Intersection Observer for scroll-triggered animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in');
          }
        });
      }, observerOptions);

      // Observe elements that should animate on scroll
      document.querySelectorAll('.card, .metric-card, .overview-card').forEach(el => {
        observer.observe(el);
      });
    }

    setupHoverEffects() {
      // Enhanced hover effects for interactive elements
      document.addEventListener('mouseover', (e) => {
        if (e.target.classList.contains('metric-card')) {
          e.target.style.transform = 'translateY(-4px) scale(1.02)';
        }
      });

      document.addEventListener('mouseout', (e) => {
        if (e.target.classList.contains('metric-card')) {
          e.target.style.transform = '';
        }
      });
    }

    setupLoadingStates() {
      // Add loading states to buttons and forms
      document.addEventListener('submit', (e) => {
        const form = e.target;
        const submitButton = form.querySelector('button[type="submit"]');
        
        if (submitButton) {
          submitButton.classList.add('loading-state');
          submitButton.disabled = true;
          
          // Remove loading state after 3 seconds (adjust as needed)
          setTimeout(() => {
            submitButton.classList.remove('loading-state');
            submitButton.disabled = false;
          }, 3000);
        }
      });
    }
  }

  // ========================================
  // RESPONSIVE UTILITIES
  // ========================================

  class AureusResponsive {
    constructor() {
      this.init();
    }

    init() {
      this.setupMobileNavigation();
      this.setupResponsiveImages();
      this.setupViewportUnits();
    }

    setupMobileNavigation() {
      // Mobile navigation enhancements
      const mobileToggle = document.querySelector('.mobile-nav-toggle');
      const navMenu = document.querySelector('.nav-menu');

      if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', () => {
          navMenu.classList.toggle('active');
          mobileToggle.setAttribute('aria-expanded', 
            navMenu.classList.contains('active')
          );
        });
      }
    }

    setupResponsiveImages() {
      // Lazy loading for images
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.classList.remove('loading');
              imageObserver.unobserve(img);
            }
          }
        });
      });

      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }

    setupViewportUnits() {
      // Fix for mobile viewport height issues
      const setVH = () => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
      };

      setVH();
      window.addEventListener('resize', setVH);
    }
  }

  // ========================================
  // PERFORMANCE OPTIMIZATIONS
  // ========================================

  class AureusPerformance {
    constructor() {
      this.init();
    }

    init() {
      this.setupDebouncing();
      this.setupPreloading();
    }

    setupDebouncing() {
      // Debounce scroll and resize events
      let scrollTimeout;
      let resizeTimeout;

      window.addEventListener('scroll', () => {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          window.dispatchEvent(new CustomEvent('scrollEnd'));
        }, 100);
      });

      window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          window.dispatchEvent(new CustomEvent('resizeEnd'));
        }, 250);
      });
    }

    setupPreloading() {
      // Preload critical resources
      const preloadLinks = document.querySelectorAll('link[rel="preload"]');
      preloadLinks.forEach(link => {
        if (link.as === 'image') {
          const img = new Image();
          img.src = link.href;
        }
      });
    }
  }

  // ========================================
  // INITIALIZATION
  // ========================================

  // Initialize all systems when DOM is ready
  function initializeAureus() {
    new AureusThemeManager();
    new AureusSmoothScroll();
    new AureusAccessibility();
    new AureusAnimations();
    new AureusResponsive();
    new AureusPerformance();

    // Dispatch initialization complete event
    window.dispatchEvent(new CustomEvent('aureusInitialized'));
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAureus);
  } else {
    initializeAureus();
  }

  // Export for external use if needed
  window.Aureus = {
    version: '1.0.0',
    initialized: true
  };

})();
