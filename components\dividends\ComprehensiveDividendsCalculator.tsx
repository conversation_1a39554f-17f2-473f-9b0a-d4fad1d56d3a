import React, { useState, useEffect, useMemo } from 'react';
import { validateKYCForFinancialOperations, KYCValidationResult } from '../../lib/kycValidation';

interface DividendsCalculatorProps {
  userShares: number;
  currentPhase?: any;
  userId?: number;
}

interface CalculationInputs {
  landHa: number;
  avgGravelThickness: number;
  inSituGrade: number;
  recoveryFactor: number;
  goldPriceUsdPerKg: number;
  opexPercent: number;
  selectedYear: number;
}

interface CalculationResults {
  numPlants: number;
  annualRevenue: number;
  annualEbit: number;
  annualGoldKg: number;
  dividendPerShare: number;
  userAnnualDividend: number;
  monthlyDividend: number;
  quarterlyDividend: number;
}

interface TrainingStep {
  id: number;
  title: string;
  content: string;
  formula?: string;
  example?: string;
}

// Constants from the working calculator
const TOTAL_SHARES = 1400000; // Correct total shares
const HECTARES_PER_PLANT = 25; // Each plant covers 25 hectares
const PLANT_CAPACITY_TPH = 200; // Tonnes per hour per plant
const EFFECTIVE_HOURS_PER_DAY = 20; // Operating hours per day
const OPERATING_DAYS_PER_YEAR = 330; // Operating days per year
const BULK_DENSITY_T_PER_M3 = 1.8; // Bulk density

// Aureus Alliance 5-year expansion plan
const EXPANSION_PLAN = {
  2026: { plants: 10, hectares: 250 },
  2027: { plants: 25, hectares: 625 },
  2028: { plants: 50, hectares: 1250 },
  2029: { plants: 100, hectares: 2500 },
  2030: { plants: 200, hectares: 5000 }
};

const TRAINING_STEPS: TrainingStep[] = [
  {
    id: 1,
    title: "Understanding Gold Production",
    content: "Gold production depends on plant capacity, operating hours, gold grade in the gravel, and recovery efficiency. Each plant processes 200 tonnes per hour for 20 hours daily, 330 days per year.",
    formula: "Annual Throughput = Plants × 200 TPH × 20 hours × 330 days\nGold Production = (Throughput × Grade/Density × Recovery) ÷ 1000",
    example: "10 plants: 10 × 200 × 20 × 330 = 13.2M tonnes\nGold: (13.2M × 0.9/1.8 × 70%) ÷ 1000 = 4,620 kg annually"
  },
  {
    id: 2,
    title: "Revenue Calculation",
    content: "Revenue is calculated by multiplying the total gold produced by the current gold price per kilogram.",
    formula: "Annual Revenue = Gold Production (kg) × Gold Price (USD/kg)",
    example: "4,620 kg × $109,026/kg = $503.7M annual revenue"
  },
  {
    id: 3,
    title: "Operating Expenses (OPEX)",
    content: "Operating expenses include mining costs, processing, labor, equipment maintenance, and overhead. This is typically 40-50% of revenue.",
    formula: "OPEX = Annual Revenue × OPEX Percentage",
    example: "$503.7M × 45% = $226.7M in operating costs"
  },
  {
    id: 4,
    title: "EBIT Calculation",
    content: "EBIT (Earnings Before Interest and Tax) is the profit available for distribution to shareholders after covering all operating expenses.",
    formula: "EBIT = Annual Revenue - Operating Expenses",
    example: "$503.7M - $226.7M = $277M EBIT"
  },
  {
    id: 5,
    title: "Dividend Distribution",
    content: "Aureus Africa distributes 100% of EBIT as dividends to shareholders. Your dividend is proportional to your share ownership.",
    formula: "Dividend Per Share = Total EBIT ÷ Total Shares Outstanding",
    example: "$277M ÷ 1,400,000 shares = $198 per share annually"
  },
  {
    id: 6,
    title: "Your Personal Dividend",
    content: "Your annual dividend is calculated by multiplying the dividend per share by the number of shares you own.",
    formula: "Your Annual Dividend = Dividend Per Share × Your Shares",
    example: "$198 × 1,000 shares = $198,000 annual dividend"
  }
];

export const ComprehensiveDividendsCalculator: React.FC<DividendsCalculatorProps> = ({
  userShares = 1000,
  currentPhase,
  userId
}) => {
  const [activeTab, setActiveTab] = useState<'calculator' | 'training' | 'projections'>('calculator');
  const [currentTrainingStep, setCurrentTrainingStep] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [kycValidation, setKycValidation] = useState<KYCValidationResult | null>(null);
  
  const [inputs, setInputs] = useState<CalculationInputs>({
    landHa: EXPANSION_PLAN[2026].hectares,
    avgGravelThickness: 0.8,
    inSituGrade: 0.9,
    recoveryFactor: 70,
    goldPriceUsdPerKg: 109026,
    opexPercent: 45,
    selectedYear: 2026
  });

  // Load KYC status if userId is provided
  useEffect(() => {
    const checkKYCStatus = async () => {
      if (userId) {
        try {
          const validation = await validateKYCForFinancialOperations(userId);
          setKycValidation(validation);
        } catch (error) {
          console.error('Error checking KYC status:', error);
        }
      }
    };

    checkKYCStatus();
  }, [userId]);

  const calculations = useMemo((): CalculationResults => {
    const { landHa, avgGravelThickness, inSituGrade, recoveryFactor, goldPriceUsdPerKg, opexPercent } = inputs;

    // Calculate number of plants needed
    const numPlants = landHa / HECTARES_PER_PLANT;

    // Calculate annual gold production using CORRECT formula from App.tsx
    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
    const annualGoldKg = (annualThroughputT * (inSituGrade / BULK_DENSITY_T_PER_M3) * (recoveryFactor / 100)) / 1000;

    // Calculate revenue and EBIT
    const annualRevenue = annualGoldKg * goldPriceUsdPerKg;
    const annualOperatingCost = annualRevenue * (opexPercent / 100);
    const annualEbit = annualRevenue - annualOperatingCost;
    
    // Calculate dividends (100% EBIT distribution)
    const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0;
    const safeUserShares = userShares || 1000; // Default to 1000 shares if undefined
    const userAnnualDividend = dividendPerShare * safeUserShares;
    const monthlyDividend = userAnnualDividend / 12;
    const quarterlyDividend = userAnnualDividend / 4;

    return {
      numPlants,
      annualRevenue,
      annualEbit,
      annualGoldKg,
      dividendPerShare,
      userAnnualDividend,
      monthlyDividend,
      quarterlyDividend
    };
  }, [inputs, userShares]);

  const handleInputChange = (field: keyof CalculationInputs, value: number) => {
    setInputs(prev => ({ ...prev, [field]: value }));
  };

  const handleYearChange = (year: number) => {
    const planData = EXPANSION_PLAN[year as keyof typeof EXPANSION_PLAN];
    if (planData) {
      setInputs(prev => ({
        ...prev,
        selectedYear: year,
        landHa: planData.hectares
      }));
    }
  };

  const renderTrainingTab = () => (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="rounded-lg p-4 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold" style={{ color: 'var(--theme-text)' }}>🎓 Dividend Training Course</h3>
          <span className="text-sm" style={{ color: 'var(--gray-600)' }}>
            Step {currentTrainingStep + 1} of {TRAINING_STEPS.length}
          </span>
        </div>
        <div className="w-full rounded-full h-2" style={{ backgroundColor: 'var(--gray-200)' }}>
          <div
            className="h-2 rounded-full transition-all duration-300"
            style={{
              backgroundColor: 'var(--primary-gold)',
              width: `${((currentTrainingStep + 1) / TRAINING_STEPS.length) * 100}%`
            }}
          />
        </div>
      </div>

      {/* Current Training Step */}
      <div className="rounded-lg p-6 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
        <div className="mb-4">
          <h4 className="text-xl font-semibold mb-2" style={{ color: 'var(--theme-text)' }}>
            {TRAINING_STEPS[currentTrainingStep].title}
          </h4>
          <p className="leading-relaxed" style={{ color: 'var(--gray-700)' }}>
            {TRAINING_STEPS[currentTrainingStep].content}
          </p>
        </div>

        {TRAINING_STEPS[currentTrainingStep].formula && (
          <div className="rounded-lg p-4 mb-4 border" style={{ backgroundColor: 'var(--gray-50)', borderColor: 'var(--gray-300)' }}>
            <h5 className="text-sm font-semibold mb-2" style={{ color: 'var(--primary-gold)' }}>📐 Formula:</h5>
            <code className="font-mono text-sm" style={{ color: 'var(--gray-800)' }}>
              {TRAINING_STEPS[currentTrainingStep].formula}
            </code>
          </div>
        )}

        {TRAINING_STEPS[currentTrainingStep].example && (
          <div className="rounded-lg p-4 border" style={{ backgroundColor: 'var(--blue-50)', borderColor: 'var(--blue-200)' }}>
            <h5 className="text-sm font-semibold mb-2" style={{ color: 'var(--blue-600)' }}>💡 Example:</h5>
            <p className="text-sm" style={{ color: 'var(--blue-700)' }}>
              {TRAINING_STEPS[currentTrainingStep].example}
            </p>
          </div>
        )}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <button
          onClick={() => setCurrentTrainingStep(Math.max(0, currentTrainingStep - 1))}
          disabled={currentTrainingStep === 0}
          className="px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          style={{
            backgroundColor: 'var(--gray-200)',
            color: 'var(--gray-700)',
            border: '1px solid var(--gray-300)'
          }}
          onMouseEnter={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = 'var(--gray-300)';
            }
          }}
          onMouseLeave={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = 'var(--gray-200)';
            }
          }}
        >
          ← Previous
        </button>
        <button
          onClick={() => setCurrentTrainingStep(Math.min(TRAINING_STEPS.length - 1, currentTrainingStep + 1))}
          disabled={currentTrainingStep === TRAINING_STEPS.length - 1}
          className="px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          style={{
            backgroundColor: 'var(--primary-gold)',
            color: 'white',
            border: '1px solid var(--primary-gold)'
          }}
          onMouseEnter={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = '#B8860B'; // Darker gold
            }
          }}
          onMouseLeave={(e) => {
            if (!e.currentTarget.disabled) {
              e.currentTarget.style.backgroundColor = 'var(--primary-gold)';
            }
          }}
        >
          Next →
        </button>
      </div>

      {/* Quick Reference */}
      <div className="rounded-lg p-4 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
        <h4 className="text-lg font-semibold mb-3" style={{ color: 'var(--theme-text)' }}>📚 Quick Reference</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <div className="mb-1" style={{ color: 'var(--gray-600)' }}>Current Gold Price:</div>
            <div className="font-semibold" style={{ color: 'var(--theme-text)' }}>${inputs.goldPriceUsdPerKg.toLocaleString()}/kg</div>
          </div>
          <div>
            <div className="mb-1" style={{ color: 'var(--gray-600)' }}>Total Shares Outstanding:</div>
            <div className="font-semibold" style={{ color: 'var(--theme-text)' }}>{TOTAL_SHARES.toLocaleString()}</div>
          </div>
          <div>
            <div className="mb-1" style={{ color: 'var(--gray-600)' }}>Your Share Count:</div>
            <div className="font-semibold" style={{ color: 'var(--theme-text)' }}>{(userShares || 1000).toLocaleString()}</div>
          </div>
          <div>
            <div className="mb-1" style={{ color: 'var(--gray-600)' }}>Dividend Payout:</div>
            <div className="font-semibold" style={{ color: 'var(--theme-text)' }}>100% of EBIT</div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderProjectionsTab = () => {
    const projections = Object.entries(EXPANSION_PLAN).map(([year, data]) => {
      const yearInputs = { ...inputs, landHa: data.hectares, selectedYear: parseInt(year) };
      const numPlants = data.plants; // Use actual plant count from expansion plan

      // Use CORRECT calculation formula
      const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
      const annualGoldKg = (annualThroughputT * (yearInputs.inSituGrade / BULK_DENSITY_T_PER_M3) * (yearInputs.recoveryFactor / 100)) / 1000;
      const annualRevenue = annualGoldKg * yearInputs.goldPriceUsdPerKg;
      const annualOperatingCost = annualRevenue * (yearInputs.opexPercent / 100);
      const annualEbit = annualRevenue - annualOperatingCost;
      const dividendPerShare = TOTAL_SHARES > 0 ? annualEbit / TOTAL_SHARES : 0;
      const userAnnualDividend = dividendPerShare * (userShares || 1000);

      return {
        year: parseInt(year),
        plants: data.plants,
        hectares: data.hectares,
        goldKg: annualGoldKg,
        revenue: annualRevenue,
        ebit: annualEbit,
        dividendPerShare,
        userDividend: userAnnualDividend
      };
    });

    return (
      <div className="space-y-6">
        {/* 5-Year Overview */}
        <div className="rounded-lg p-6 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
          <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--theme-text)' }}>📈 5-Year Dividend Projections</h3>
          <p className="mb-6" style={{ color: 'var(--gray-600)' }}>
            Based on Aureus Alliance's expansion plan and your current {(userShares || 1000).toLocaleString()} shares
          </p>

          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b" style={{ borderColor: 'var(--theme-border)' }}>
                  <th className="text-left py-2" style={{ color: 'var(--gray-700)' }}>Year</th>
                  <th className="text-left py-2" style={{ color: 'var(--gray-700)' }}>Plants</th>
                  <th className="text-left py-2" style={{ color: 'var(--gray-700)' }}>Hectares</th>
                  <th className="text-left py-2" style={{ color: 'var(--gray-700)' }}>Gold (kg)</th>
                  <th className="text-left py-2" style={{ color: 'var(--gray-700)' }}>Revenue</th>
                  <th className="text-left py-2" style={{ color: 'var(--gray-700)' }}>Your Dividend</th>
                </tr>
              </thead>
              <tbody>
                {projections.map((proj, index) => (
                  <tr key={proj.year} className="border-b" style={{ borderColor: 'var(--gray-200)' }}>
                    <td className="py-3 font-semibold" style={{ color: 'var(--theme-text)' }}>{proj.year}</td>
                    <td className="py-3" style={{ color: 'var(--gray-600)' }}>{proj.plants}</td>
                    <td className="py-3" style={{ color: 'var(--gray-600)' }}>{proj.hectares}</td>
                    <td className="py-3" style={{ color: 'var(--gray-600)' }}>{proj.goldKg.toFixed(0)}</td>
                    <td className="py-3" style={{ color: 'var(--gray-600)' }}>${(proj.revenue / 1000000).toFixed(1)}M</td>
                    <td className="py-3 font-semibold" style={{ color: 'var(--green-600)' }}>
                      ${proj.userDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Growth Visualization */}
        <div className="rounded-lg p-6 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
          <h4 className="text-lg font-semibold mb-4" style={{ color: 'var(--theme-text)' }}>📊 Dividend Growth Visualization</h4>
          <div className="space-y-3">
            {projections.map((proj, index) => {
              const maxDividend = Math.max(...projections.map(p => p.userDividend));
              const barWidth = (proj.userDividend / maxDividend) * 100;

              return (
                <div key={proj.year} className="flex items-center space-x-4">
                  <div className="w-12 text-sm" style={{ color: 'var(--gray-600)' }}>{proj.year}</div>
                  <div className="flex-1 rounded-full h-6 relative" style={{ backgroundColor: 'var(--gray-200)' }}>
                    <div
                      className="bg-gradient-to-r from-blue-600 to-green-500 h-6 rounded-full transition-all duration-1000"
                      style={{ width: `${barWidth}%` }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center text-xs font-semibold text-white">
                      ${proj.userDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Key Insights */}
        <div className="rounded-lg p-6 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
          <h4 className="text-lg font-semibold mb-4" style={{ color: 'var(--theme-text)' }}>💡 Key Insights</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="rounded-lg p-4 border" style={{ backgroundColor: 'var(--green-50)', borderColor: 'var(--green-200)' }}>
              <div className="font-semibold mb-2" style={{ color: 'var(--green-600)' }}>Total 5-Year Dividends</div>
              <div className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
                ${projections.reduce((sum, proj) => sum + proj.userDividend, 0).toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </div>
            </div>
            <div className="rounded-lg p-4 border" style={{ backgroundColor: 'var(--blue-50)', borderColor: 'var(--blue-200)' }}>
              <div className="font-semibold mb-2" style={{ color: 'var(--blue-600)' }}>Peak Annual Dividend (2030)</div>
              <div className="text-2xl font-bold" style={{ color: 'var(--theme-text)' }}>
                ${projections[projections.length - 1].userDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderHelpTab = () => (
    <div className="space-y-6">
      {/* FAQ Section */}
      <div className="rounded-lg p-6 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
        <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--theme-text)' }}>❓ Frequently Asked Questions</h3>

        <div className="space-y-4">
          <div className="border-b pb-4" style={{ borderColor: 'var(--theme-border)' }}>
            <h4 className="text-lg font-semibold mb-2" style={{ color: 'var(--blue-600)' }}>
              How are dividends calculated?
            </h4>
            <p style={{ color: 'var(--gray-600)' }}>
              Dividends are calculated based on the company's EBIT (Earnings Before Interest and Tax).
              Aureus Alliance distributes 100% of EBIT as dividends to shareholders. Your dividend is
              proportional to your share ownership out of the total 10 million shares.
            </p>
          </div>

          <div className="border-b pb-4" style={{ borderColor: 'var(--theme-border)' }}>
            <h4 className="text-lg font-semibold mb-2" style={{ color: 'var(--blue-600)' }}>
              When are dividends paid?
            </h4>
            <p style={{ color: 'var(--gray-600)' }}>
              Dividends are typically paid quarterly (every 3 months) based on the actual gold
              production and sales. The exact payment dates depend on mining operations and
              gold market conditions.
            </p>
          </div>

          <div className="border-b pb-4" style={{ borderColor: 'var(--theme-border)' }}>
            <h4 className="text-lg font-semibold mb-2" style={{ color: 'var(--blue-600)' }}>
              What factors affect my dividend amount?
            </h4>
            <p style={{ color: 'var(--gray-600)' }}>
              Your dividend depends on: (1) Number of shares you own, (2) Gold production volume,
              (3) Gold market price, (4) Operating costs, and (5) Company expansion phase.
              As Aureus Alliance expands operations, dividend potential increases significantly.
            </p>
          </div>

          <div className="border-b pb-4" style={{ borderColor: 'var(--theme-border)' }}>
            <h4 className="text-lg font-semibold mb-2" style={{ color: 'var(--blue-600)' }}>
              How accurate are these projections?
            </h4>
            <p style={{ color: 'var(--gray-600)' }}>
              Projections are estimates based on geological surveys, current market conditions,
              and the company's expansion plan. Actual results may vary due to gold price
              fluctuations, operational challenges, or changes in mining conditions.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-2" style={{ color: 'var(--blue-600)' }}>
              Can I reinvest my dividends?
            </h4>
            <p style={{ color: 'var(--gray-600)' }}>
              Yes! You can use dividend payments to purchase additional shares during any phase,
              allowing you to compound your holdings and increase future dividend potential.
            </p>
          </div>
        </div>
      </div>

      {/* Key Terms */}
      <div className="rounded-lg p-6 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
        <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--theme-text)' }}>📚 Key Terms</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <div className="font-semibold" style={{ color: 'var(--primary-gold)' }}>EBIT</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Earnings Before Interest and Tax - the company's profit available for distribution
              </div>
            </div>
            <div>
              <div className="font-semibold" style={{ color: 'var(--primary-gold)' }}>OPEX</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Operating Expenses - costs of running mining operations (labor, equipment, etc.)
              </div>
            </div>
            <div>
              <div className="font-semibold" style={{ color: 'var(--primary-gold)' }}>Recovery Factor</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Percentage of gold successfully extracted from processed gravel
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <div className="font-semibold" style={{ color: 'var(--primary-gold)' }}>Gravel Thickness</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Average depth of gold-bearing gravel layer in meters
              </div>
            </div>
            <div>
              <div className="font-semibold" style={{ color: 'var(--primary-gold)' }}>In-Situ Grade</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Concentration of gold in the ground before processing (grams per cubic meter)
              </div>
            </div>
            <div>
              <div className="font-semibold" style={{ color: 'var(--primary-gold)' }}>Dividend Yield</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Annual dividend as a percentage of your share purchase price
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tips for Maximizing Dividends */}
      <div className="rounded-lg p-6 border" style={{ backgroundColor: 'var(--theme-card-bg)', borderColor: 'var(--theme-border)' }}>
        <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--theme-text)' }}>💡 Tips for Maximizing Dividends</h3>

        <div className="space-y-3">
          <div className="flex items-start space-x-3">
            <div className="text-xl" style={{ color: 'var(--green-600)' }}>1.</div>
            <div>
              <div className="font-semibold" style={{ color: 'var(--theme-text)' }}>Buy Early in Each Phase</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Share prices increase with each phase. Buying early maximizes your share count for the same amount.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-xl" style={{ color: 'var(--green-600)' }}>2.</div>
            <div>
              <div className="font-semibold" style={{ color: 'var(--theme-text)' }}>Reinvest Dividends</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Use dividend payments to buy more shares, creating a compounding effect on future dividends.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-xl" style={{ color: 'var(--green-600)' }}>3.</div>
            <div>
              <div className="font-semibold" style={{ color: 'var(--theme-text)' }}>Monitor Gold Prices</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Higher gold prices directly increase dividend potential. Stay informed about market trends.
              </div>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <div className="text-xl" style={{ color: 'var(--green-600)' }}>4.</div>
            <div>
              <div className="font-semibold" style={{ color: 'var(--theme-text)' }}>Understand the Expansion Plan</div>
              <div className="text-sm" style={{ color: 'var(--gray-600)' }}>
                Each expansion phase significantly increases production capacity and dividend potential.
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="rounded-lg p-6 border" style={{ backgroundColor: 'var(--blue-50)', borderColor: 'var(--blue-200)' }}>
        <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--theme-text)' }}>📞 Need More Help?</h3>
        <p className="mb-4" style={{ color: 'var(--blue-700)' }}>
          Our team is here to help you understand your dividend potential and make informed decisions.
        </p>
        <div className="space-y-2 text-sm">
          <div style={{ color: 'var(--blue-600)' }}>
            📧 Email: <EMAIL>
          </div>
          <div style={{ color: 'var(--blue-600)' }}>
            💬 Telegram: @AureusSupport
          </div>
          <div style={{ color: 'var(--blue-600)' }}>
            📱 WhatsApp: Available through your dashboard
          </div>
        </div>
      </div>
    </div>
  );

  const renderCalculatorTab = () => (
    <div className="calculator-tab-content">
      {/* Enhanced Quick Stats */}
      <div className="stats-grid">
        <div className="stat-card shares">
          <div className="stat-header">
            <div className="stat-icon">📊</div>
            <div className="stat-info">
              <div className="stat-label">Your Shares</div>
              <div className="stat-description">Total share ownership</div>
            </div>
          </div>
          <div className="stat-value">{(userShares || 1000).toLocaleString()}</div>
          <div className="stat-percentage">
            {(((userShares || 1000) / TOTAL_SHARES) * 100).toFixed(4)}% of total
          </div>
        </div>

        <div className="stat-card annual">
          <div className="stat-header">
            <div className="stat-icon">💰</div>
            <div className="stat-info">
              <div className="stat-label">Annual Dividend</div>
              <div className="stat-description">Yearly earnings projection</div>
            </div>
          </div>
          <div className="stat-value">${calculations.userAnnualDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
          <div className="stat-percentage">
            ${calculations.dividendPerShare.toFixed(2)} per share
          </div>
        </div>

        <div className="stat-card monthly">
          <div className="stat-header">
            <div className="stat-icon">📅</div>
            <div className="stat-info">
              <div className="stat-label">Monthly Dividend</div>
              <div className="stat-description">Monthly income stream</div>
            </div>
          </div>
          <div className="stat-value">${calculations.monthlyDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })}</div>
          <div className="stat-percentage">
            ${calculations.quarterlyDividend.toLocaleString(undefined, { maximumFractionDigits: 0 })} quarterly
          </div>
        </div>
      </div>

      {/* Enhanced Year Selection */}
      <div className="year-selection-section">
        <div className="section-header">
          <div className="section-icon">📅</div>
          <div className="section-info">
            <h3 className="section-title">Select Expansion Year</h3>
            <p className="section-description">Choose a year from our 5-year expansion plan to see projected dividends</p>
          </div>
        </div>

        <div className="year-grid">
          {Object.entries(EXPANSION_PLAN).map(([year, data]) => (
            <button
              key={year}
              onClick={() => handleYearChange(parseInt(year))}
              className={`year-card ${inputs.selectedYear === parseInt(year) ? 'active' : ''}`}
            >
              <div className="year-header">
                <div className="year-number">{year}</div>
                <div className="year-status">
                  {inputs.selectedYear === parseInt(year) ? '✓' : '○'}
                </div>
              </div>
              <div className="year-details">
                <div className="year-stat">
                  <span className="stat-icon">🏭</span>
                  <span className="stat-text">{data.plants} plants</span>
                </div>
                <div className="year-stat">
                  <span className="stat-icon">🗺️</span>
                  <span className="stat-text">{data.hectares} ha</span>
                </div>
              </div>
              <div className="year-production">
                <span className="production-label">Est. Gold Production</span>
                <span className="production-value">
                  {(() => {
                    const numPlants = data.hectares / HECTARES_PER_PLANT;
                    const annualThroughputT = numPlants * PLANT_CAPACITY_TPH * EFFECTIVE_HOURS_PER_DAY * OPERATING_DAYS_PER_YEAR;
                    const annualGoldKg = (annualThroughputT * (inputs.inSituGrade / BULK_DENSITY_T_PER_M3) * (inputs.recoveryFactor / 100)) / 1000;
                    return `${annualGoldKg.toFixed(0)}kg`;
                  })()}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Enhanced Advanced Parameters */}
      <div className="advanced-parameters-section">
        <div className="advanced-header">
          <div className="advanced-title">
            <div className="advanced-icon">⚙️</div>
            <div className="advanced-info">
              <h3 className="advanced-label">Advanced Parameters</h3>
              <p className="advanced-description">Fine-tune calculation variables for custom scenarios</p>
            </div>
          </div>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`advanced-toggle ${showAdvanced ? 'active' : ''}`}
          >
            <span className="toggle-icon">{showAdvanced ? '▼' : '▶'}</span>
            <span className="toggle-text">{showAdvanced ? 'Hide' : 'Show'} Advanced</span>
          </button>
        </div>

        {showAdvanced && (
          <div className="advanced-content">
            <div className="parameters-grid">
              <div className="parameter-input-group">
                <div className="input-header">
                  <div className="input-icon">🥇</div>
                  <div className="input-info">
                    <label className="input-label">Gold Price (USD/kg)</label>
                    <span className="input-description">Current market price per kilogram</span>
                  </div>
                </div>
                <div className="input-wrapper">
                  <span className="input-prefix">$</span>
                  <input
                    type="number"
                    value={inputs.goldPriceUsdPerKg}
                    onChange={(e) => handleInputChange('goldPriceUsdPerKg', parseFloat(e.target.value) || 0)}
                    className="parameter-input"
                    placeholder="109026"
                  />
                </div>
              </div>

              <div className="parameter-input-group">
                <div className="input-header">
                  <div className="input-icon">💸</div>
                  <div className="input-info">
                    <label className="input-label">OPEX Percentage (%)</label>
                    <span className="input-description">Operating expenses as % of revenue</span>
                  </div>
                </div>
                <div className="input-wrapper">
                  <input
                    type="number"
                    value={inputs.opexPercent}
                    onChange={(e) => handleInputChange('opexPercent', parseFloat(e.target.value) || 0)}
                    className="parameter-input"
                    placeholder="45"
                  />
                  <span className="input-suffix">%</span>
                </div>
              </div>

              <div className="parameter-input-group">
                <div className="input-header">
                  <div className="input-icon">📏</div>
                  <div className="input-info">
                    <label className="input-label">Gravel Thickness (m)</label>
                    <span className="input-description">Average depth of gold-bearing gravel</span>
                  </div>
                </div>
                <div className="input-wrapper">
                  <input
                    type="number"
                    step="0.1"
                    value={inputs.avgGravelThickness}
                    onChange={(e) => handleInputChange('avgGravelThickness', parseFloat(e.target.value) || 0)}
                    className="parameter-input"
                    placeholder="0.8"
                  />
                  <span className="input-suffix">m</span>
                </div>
              </div>

              <div className="parameter-input-group">
                <div className="input-header">
                  <div className="input-icon">⚡</div>
                  <div className="input-info">
                    <label className="input-label">Recovery Factor (%)</label>
                    <span className="input-description">Gold extraction efficiency rate</span>
                  </div>
                </div>
                <div className="input-wrapper">
                  <input
                    type="number"
                    value={inputs.recoveryFactor}
                    onChange={(e) => handleInputChange('recoveryFactor', parseFloat(e.target.value) || 0)}
                    className="parameter-input"
                    placeholder="70"
                  />
                  <span className="input-suffix">%</span>
                </div>
              </div>
            </div>

            {/* Parameter Impact Summary */}
            <div className="parameter-impact">
              <h4 className="impact-title">Parameter Impact Summary</h4>
              <div className="impact-grid">
                <div className="impact-item">
                  <span className="impact-label">Annual Revenue:</span>
                  <span className="impact-value">${(calculations.annualRevenue / 1000000).toFixed(1)}M</span>
                </div>
                <div className="impact-item">
                  <span className="impact-label">Operating Costs:</span>
                  <span className="impact-value">${((calculations.annualRevenue * inputs.opexPercent / 100) / 1000000).toFixed(1)}M</span>
                </div>
                <div className="impact-item">
                  <span className="impact-label">EBIT:</span>
                  <span className="impact-value">${(calculations.annualEbit / 1000000).toFixed(1)}M</span>
                </div>
                <div className="impact-item">
                  <span className="impact-label">Dividend Pool:</span>
                  <span className="impact-value">${(calculations.annualEbit / 1000000).toFixed(1)}M</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="enhanced-calculator-wrapper">
      {/* Enhanced Header */}
      <div className="calculator-header">
        <div className="header-content">
          <div className="header-icon">💰</div>
          <div className="header-text">
            <h2 className="header-title">Dividends Calculator</h2>
            <p className="header-subtitle">
              Calculate your potential dividends based on real gold production data and our 5-year expansion plan
            </p>
          </div>
        </div>
        <div className="header-stats">
          <div className="stat-item">
            <span className="stat-icon">🏭</span>
            <span className="stat-label">Active Plants</span>
            <span className="stat-value">{calculations.numPlants.toFixed(0)}</span>
          </div>
          <div className="stat-item">
            <span className="stat-icon">🥇</span>
            <span className="stat-label">Gold Production</span>
            <span className="stat-value">{calculations.annualGoldKg.toFixed(0)}kg</span>
          </div>
        </div>
      </div>

      {/* KYC Notice for Dividend Payments */}
      {userId && kycValidation && (
        <div className={`rounded-lg p-4 mb-6 border ${
          kycValidation.canReceiveDividends
            ? 'bg-green-900/20 border-green-500/30'
            : 'bg-yellow-900/20 border-yellow-500/30'
        }`}>
          <div className="flex items-start space-x-3">
            <div className="text-2xl">
              {kycValidation.canReceiveDividends ? '✅' : '⚠️'}
            </div>
            <div>
              <h4 className="font-semibold text-white mb-1">
                {kycValidation.canReceiveDividends ? 'Ready for Dividends' : 'KYC Required for Dividend Payments'}
              </h4>
              <p className={`text-sm ${
                kycValidation.canReceiveDividends ? 'text-green-300' : 'text-yellow-300'
              }`}>
                {kycValidation.canReceiveDividends
                  ? 'Your KYC is verified. You can receive dividend payments when they become available.'
                  : 'Complete KYC verification to receive dividend payments. You can still calculate potential dividends below.'
                }
              </p>
              {!kycValidation.canReceiveDividends && (
                <button
                  onClick={() => window.location.href = '#kyc'}
                  className="mt-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm px-3 py-1 rounded transition-colors"
                >
                  Complete KYC
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Tab Navigation */}
      <div className="calculator-tabs">
        {[
          { key: 'calculator', label: 'Calculator', icon: '🧮', description: 'Interactive calculator' },
          { key: 'training', label: 'Training', icon: '🎓', description: 'Learn how it works' },
          { key: 'projections', label: 'Projections', icon: '📈', description: '5-year forecasts' },
          { key: 'help', label: 'Help', icon: '❓', description: 'Support & FAQ' }
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`calculator-tab ${activeTab === tab.key ? 'active' : ''}`}
          >
            <div className="tab-icon">{tab.icon}</div>
            <div className="tab-content">
              <div className="tab-label">{tab.label}</div>
              <div className="tab-description">{tab.description}</div>
            </div>
          </button>
        ))}
      </div>

      {/* Enhanced Tab Content */}
      <div className="calculator-content">
        {activeTab === 'calculator' && renderCalculatorTab()}
        {activeTab === 'training' && renderTrainingTab()}
        {activeTab === 'projections' && renderProjectionsTab()}
        {activeTab === 'help' && renderHelpTab()}
      </div>
    </div>
  );
};
